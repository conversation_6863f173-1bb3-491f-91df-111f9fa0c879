# 服务器配置
server:
  port: 28081
  servlet:
    context-path: /aggregate-pay

# 数据库配置
spring:
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: **************************************************************************************************************************************************************************
    username: db_fzd_test
    password: '!QAZ1qazFzd'
  #    url: **********************************************************************************************************************************************************
  #    username: root
  #    password: '0226'
  # Redis配置
#  redis:
#    host: localhost
#    port: 6379
#    database: 0
#    password:
#    timeout: 10s
#    lettuce:
#      pool:
#        min-idle: 0
#        max-idle: 8
#        max-active: 8
#        max-wait: -1ms

# MyBatis Plus配置
mybatis-plus:
  mapper-locations: classpath*:mapper/**/*Mapper.xml
  type-aliases-package: com.aggregate.payment.entity
  configuration:
    map-underscore-to-camel-case: true
    cache-enabled: false
    call-setters-on-nulls: true
    jdbc-type-for-null: 'null'

# 日志配置
logging:
  level:
    com.aggregate.payment: debug
    org.springframework: warn

# 聚合支付配置
aggregate:
  payment:
    rate: 5
    points_coefficient: 'points_coefficient'
    bank-id: '000000'
    dictKey: 'plat_bank_code_test'
    totalPeriods: 'commission_total_periods'
    RedirectUrl: 'https://test.fengzhidi.com/payment.html'
    payurl: 'http://127.0.0.1:28888/pay-service/v1/order/onlinePayOrder/'
    payConfirm: 'http://127.0.0.1:28888/pay-service/v1/order/mrchAvlCnfrmRcpt'
    refundUrl: 'http://127.0.0.1:28888/pay-service/v1/order/mrchAvlPyRfnd'
    # 域名配置
    domain: 'https://test.fengzhidi.com'
    QRdomain: 'https://test.fengzhidi.com'
    # 微信配置
    wechat:
      app-id: wx98329875e25138a4
      app-secret: 80a78d4795742642debc72af74e8c984
    # 支付宝配置
    alipay:
      app-id: ****************
      public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEAtjoica7E1h2KZgay+KIBvBKqvPP7NKwIRogGPK3XuYr9iWKINmWBJu4Xk5pZkNLPZfzy+RS6r3r9o2lLlysVZlhyv9q8Lbds0b/n6/mtk45BeR7ch9ce/zEq86kaUSTWhgwAetR4ldG/Zskq43wbJod++v40Qx7MCSvvksqP2LTx+pA0Y6JgXe20IjS3CGY0uYWmh1buDFNr1j7y40Or6FOiwvi8CXFler6JEA8dftKQwgzTJyrMrT64GlK5ZicnScP9Dus7G/mhve3C/XLurohMF9T5ILO8iuPtOddChDxXrJ6Of6HT4WWBVtNNxeK7vl3RL5qve7urDXk9Dsg9YwIDAQAB
      private-key: MIIEvAIBADANBgkqhkiG9w0BAQEFAASCBKYwggSiAgEAAoIBAQCBX1zaMzo07Q5VuPYoJ27LNRMMkYRccZ89VK/uqDD+ssf5Bh9YhENVLv8CdNMrxUnIS80rwajjLK/7wK6f6vrJ9CQsU6PPanC2UoVxvsstP5VVRp+jJoycENgcrvVmNuLCZ/45BMrDqWTO8a6pY4j9K6bBr7yl4a42BB0ksMO9tqVC0G5AszzgwGO+8AOLNjrcLo/k649HmH7PNkTXe6YoONIpvNf37Ux67cYJ9RpSJ/2BRjyOTAw1cvj3scswYbE2U8Al7kL4+nvDs3ayPH5rsXw2hQlKJUflFpfdcrDyIAi3H7AIqHQFawLByW2Oxw6SMt1hJK5iLCNz2PB1AS95AgMBAAECggEANjYFrJFmxKLJLPaSf4T2kkQtNUkM08fw2DxSdIkZ7sBvt1PvcqIJrPpCdX8Ktf63yiypTrqZ0If7Pl0hT3WhnVqmhcSOke2iSWQO8vjmHpHUO6WaYz1WElekl3Bg+HHrV1dIWhA9/vtOeMEhVW2WC7Bs8iB3xnLpE01qAOPpQedWDQgKevfrHN/+t49st9akBntH9T79CZzWF6RzQvHCQpkI+N5alihbDgBST7nY3OBluhv7N1r4gXbn/Cjtetdq99lEsgK45eIFLooL1K78ho2o+faJEs+ViP4foXCw7No2B1+0DvhC8MDNbg4tCkSB1vVoXdvVTSUMJ9tbmw4RYQKBgQDzqzEdZr8p0qCsA0zlP9alqktiXbWCWKliHqkvodKlIbyDxnOrvNioaDD1gOiOcos05WXZkZzDojNinIqDJniqao99I1bZRp8PwjNxmlg2lSGDwZgGSw5fCi1DNO9/3TbBADPmzyiOSOArta3AG08Ixn8d/HLBOnWmP4tEix7x5QKBgQCH620OaeGF4DZRZoa4lHavFLKM4xRlQ0MDN7ItBgi19iQy7RuvxxZ+dww8IQ02SoFL/PCJ4m6qNZ+9QYv7D11USXsF9TqrX6mkV7eE++ZIa552FUl1FbeUctgZDsC7YwAJQt1vfCrYNc4RgbfBIoophcHPu3ZK0C0ghE8hen0+BQKBgEQQ5yhiYjYJm05RobJMUeZ3GpXAvYpKwA4jqHkgyBnOwb1Bu2AW4aKxcDO0LcJU5uQAzKGpFlRjw9zhA6Q9I+ne2HRJYeJR4u7g/cPXLJsar4EBnDjt0Hbl/dzPNbPPFJN3PghUh+yvGMe8+Ws3da1q3Wl/8oJdlS26hGnCPwOtAoGAOLwQvZ98v8Ejaz78pTdzgwmErkJrVQmKg2FQQnLuV+Xynj9S+0CDJy7SMJcrSKo45lsinKujvfC8SOd/YsC7GF/DtmzynG5GBY+Hzz7GlRw2/NtN2k3CX3jAs1TxJkbg0PzUKaEO0UlERxJ6fWfJfCOVDv0VF41mCNixnyvd83ECgYAf2QqwNhmbR2u1I12bM+jScbp8dGPEcIu4EhPql3mWyi+dkwd60Id3yihlDZ96LN55f2R3jRFov4qV+di095Era/HzOqL7TpPE8ygEPpA1/NwTWnzzocCR7NzvliJtrvPPO2cgt7wYs5a4c77LpGswBMA5bqdPAZq0u0/VG5HGUA==
      gateway-url: https://openapi.alipay.com/gateway.do
jiguang:
  api:
    # 此为演示数据，请替换成真实数据
    app-key: 7af319024076a5414fce0cb1
    master-secret: f99c7264ea3cdcde93150b19

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aggregate.payment.mapper.OfflineOrderMapper">
    
    <resultMap id="OfflineOrderResult" type="com.aggregate.payment.entity.OfflineOrder">
        <id property="id" column="id"/>
        <result property="merId" column="mer_id"/>
        <result property="orderCode" column="order_code"/>
        <result property="commissionRate" column="commission_rate"/>
        <result property="buyerOpenid" column="buyer_openid"/>
        <result property="account" column="account"/>
        <result property="productName" column="product_name"/>
        <result property="productDescription" column="product_description"/>
        <result property="paymentAmount" column="payment_amount"/>
        <result property="payState" column="pay_state"/>
        <result property="orderState" column="order_state"/>
        <result property="commissionMer" column="commission_mer"/>
        <result property="commissionPl" column="commission_pl"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="ip" column="ip"/>
        <result property="mac" column="mac"/>
        <result property="wsId" column="ws_id"/>
        <result property="bankUserCode" column="bank_user_code"/>
        <result property="platBankCode" column="plat_bank_code"/>
        <result property="spreadUid" column="spread_uid"/>
        <result property="uid" column="uid"/>
        <result property="serviceProviderType" column="service_provider_type"/>
        <result property="serviceProviderId" column="service_provider_id"/>
        <result property="serviceProviderName" column="service_provider_name"/>
        <result property="mrchSno" column="mrch_sno"/>
        <result property="merName" column="mer_name"/>
    </resultMap>
    
    <sql id="selectOfflineOrderVo">
        select id, mer_id,  commission_rate, order_code, buyer_openid, account,
               product_name, product_description, payment_amount, pay_state, order_state,
               commission_mer, commission_pl, create_time, update_time, ip, mac, ws_id,
               plat_bank_code, bank_user_code, spread_uid, uid, service_provider_type,
               service_provider_id, service_provider_name, mrch_sno
        from ml_offline_order

    </sql>
    <sql id="selectOfflineOrderVo4Pay">
        select id, mer_id, commission_rate, order_code,buyer_openid, account, product_name, product_description,
               payment_amount, pay_state, order_state, commission_mer, commission_pl, create_time, update_time,ip,mac,ws_id,plat_bank_code,bank_user_code,spread_uid,uid,service_provider_type,service_provider_id,service_provider_name,mrch_sno
        from ml_offline_order_temp
    </sql>
    <delete id="deleteSuccessOrdersTemp">
        delete from ml_offline_order_temp where order_code in
        <foreach collection="orderCodeList" item="orderCode" open="(" separator="," close=")">
            #{orderCode}
        </foreach>
    </delete>
    <delete id="deleteRefundTempOrder">
        delete from ml_offline_order_temp where order_code = #{orderCode}
    </delete>

    <!-- 查询线下订单列表 -->
    <select id="selectOfflineOrderList" parameterType="com.aggregate.payment.entity.OfflineOrder" resultMap="OfflineOrderResult">
        select o.id, o.mer_id, m.mer_name, o.commission_rate, o.order_code, o.buyer_openid, o.account,
        o.product_name, o.product_description, o.payment_amount, o.pay_state, o.order_state,
        o.commission_mer, o.commission_pl, o.create_time, o.update_time, o.ip, o.mac, o.ws_id,
        o.plat_bank_code, o.bank_user_code, o.spread_uid, o.uid, o.service_provider_type,
        o.service_provider_id, o.service_provider_name, o.mrch_sno
        from ml_offline_order o
        left join ml_merchant m on o.mer_id = m.mer_id
        <where>
            <if test="id != null">
                and o.id = #{id}
            </if>
            <if test="merId != null">
                and o.mer_id = #{merId}
            </if>
            <if test="buyerOpenid != null and buyerOpenid != ''">
                and o.buyer_openid = #{buyerOpenid}
            </if>
            <if test="orderCode != null and orderCode != ''">
                and o.order_code = #{orderCode}
            </if>
            <if test="account != null and account != ''">
                and o.account = #{account}
            </if>
            <if test="payState != null">
                and o.pay_state = #{payState}
            </if>
            <if test="orderState != null">
                and o.order_state = #{orderState}
            </if>
            <if test="wsId != null and wsId != ''">
                and o.ws_id = #{wsId}
            </if>
            <if test="mac != null and mac != ''">
                and o.mac = #{mac}
            </if>
            <if test="ip != null and ip != ''">
                and o.ip = #{ip}
            </if>
            <if test="spreadUid != null">
                and o.spread_uid = #{spreadUid}
            </if>
            <if test="serviceProviderType != null">
                and o.service_provider_type = #{serviceProviderType}
            </if>
            <if test="serviceProviderId != null">
                and o.service_provider_id = #{serviceProviderId}
            </if>
            <if test="serviceProviderName != null and serviceProviderName != ''">
                and o.service_provider_name = #{serviceProviderName}
            </if>
            <if test="startTime != null">
                and o.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and o.create_time &lt;= #{endTime}
            </if>
        </where>

        order by create_time desc
        limit #{pageSize} offset #{pageNum}
    </select>
    <select id="selectOfflineOrderListTemp" parameterType="com.aggregate.payment.entity.OfflineOrder" resultMap="OfflineOrderResult">
        select id, mer_id, commission_rate, order_code,buyer_openid, account, product_name, product_description,
        payment_amount, pay_state, order_state, commission_mer, commission_pl, create_time, update_time,ip,mac,ws_id,plat_bank_code,bank_user_code,spread_uid,uid,service_provider_type,service_provider_id,service_provider_name,mrch_sno
        from ml_offline_order_temp ot
        <where>
            <if test="id != null">
                and ot.id = #{id}
            </if>
            <if test="merId != null">
                and ot.mer_id = #{merId}
            </if>
            <if test="buyerOpenid != null and buyerOpenid != ''">
                and ot.buyer_openid = #{buyerOpenid}
            </if>
            <if test="orderCode != null and orderCode != ''">
                and ot.order_code = #{orderCode}
            </if>
            <if test="account != null and account != ''">
                and ot.account = #{account}
            </if>
            <if test="payState != null">
                and ot.pay_state = #{payState}
            </if>
            <if test="orderState != null">
                and ot.order_state = #{orderState}
            </if>
            <if test="wsId != null and wsId != ''">
                and ot.ws_id = #{wsId}
            </if>
            <if test="mac != null and mac != ''">
                and ot.mac = #{mac}
            </if>
            <if test="ip != null and ip != ''">
                and ot.ip = #{ip}
            </if>
            <if test="spreadUid != null">
                and ot.spread_uid = #{spreadUid}
            </if>
            <if test="serviceProviderType != null">
                and ot.service_provider_type = #{serviceProviderType}
            </if>
            <if test="serviceProviderId != null">
                and ot.service_provider_id = #{serviceProviderId}
            </if>
            <if test="serviceProviderName != null and serviceProviderName != ''">
                and ot.service_provider_name = #{serviceProviderName}
            </if>
            <if test="startTime != null">
                and ot.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and ot.create_time &lt;= #{endTime}
            </if>
        </where>

        order by create_time desc
        limit #{pageSize} offset #{pageNum}
    </select>

    
    <!-- 根据ID查询线下订单 -->
    <select id="selectOfflineOrderById" parameterType="Long" resultMap="OfflineOrderResult">
        <include refid="selectOfflineOrderVo"/>
        where id = #{id}
    </select>
    
    <!-- 根据商户ID查询订单 -->
    <select id="selectOrdersByMerId" parameterType="Long" resultMap="OfflineOrderResult">
        <include refid="selectOfflineOrderVo"/>
        where mer_id = #{merId}
        order by create_time desc
    </select>
    
    <!-- 根据购买者openid查询订单 -->
    <select id="selectOrdersByBuyerOpenid" parameterType="String" resultMap="OfflineOrderResult">
        <include refid="selectOfflineOrderVo"/>
        where buyer_openid = #{buyerOpenid}
        order by create_time desc
    </select>
<!--    <select id="selectOfflineOrderByIdList" resultType="com.aggregate.payment.entity.OfflineOrder">-->
<!--        <include refid="selectOfflineOrderVo"/>-->
<!--        where id IN-->
<!--        <foreach collection="ids" item="id" open="(" separator="," close=")">-->
<!--            #{id}-->
<!--        </foreach>-->
<!--    </select>-->
    <select id="selectOfflineUFOrders" resultType="com.aggregate.payment.entity.OfflineOrder">
        <include refid="selectOfflineOrderVo"/>
        where order_state = 0
        order by create_time desc
    </select>
    <select id="selectOfflineOrderList4Pay" resultType="com.aggregate.payment.entity.OfflineOrder">
        <include refid="selectOfflineOrderVo4Pay"/>
        where pay_state = 1 and order_state = 0
    </select>
    <select id="selectOfflineOrderCount" resultType="java.lang.Integer">
        select count(*) from ml_offline_order o
        <where>
            <if test="id != null">
                and o.id = #{id}
            </if>
            <if test="merId != null">
                and o.mer_id = #{merId}
            </if>
            <if test="buyerOpenid != null and buyerOpenid != ''">
                and o.buyer_openid = #{buyerOpenid}
            </if>
            <if test="orderCode != null and orderCode != ''">
                and o.order_code = #{orderCode}
            </if>
            <if test="account != null and account != ''">
                and o.account = #{account}
            </if>
            <if test="payState != null">
                and o.pay_state = #{payState}
            </if>
            <if test="orderState != null">
                and o.order_state = #{orderState}
            </if>
            <if test="wsId != null and wsId != ''">
                and o.ws_id = #{wsId}
            </if>
            <if test="mac != null and mac != ''">
                and o.mac = #{mac}
            </if>
            <if test="ip != null and ip != ''">
                and o.ip = #{ip}
            </if>
            <if test="spreadUid != null">
                and o.spread_uid = #{spreadUid}
            </if>
            <if test="serviceProviderType != null">
                and o.service_provider_type = #{serviceProviderType}
            </if>
            <if test="serviceProviderId != null">
                and o.service_provider_id = #{serviceProviderId}
            </if>
            <if test="serviceProviderName != null and serviceProviderName != ''">
                and o.service_provider_name = #{serviceProviderName}
            </if>
            <if test="startTime != null">
                and o.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and o.create_time &lt;= #{endTime}
            </if>
        </where>
    </select>
    <select id="selectByOrderCode" resultType="com.aggregate.payment.entity.OfflineOrder">
        <include refid="selectOfflineOrderVo4Pay"/>
        where order_code = #{orderCode}
    </select>
    <select id="selectOfflineOrderByOrderCode" parameterType="String" resultMap="OfflineOrderResult">
        <include refid="selectOfflineOrderVo4Pay"/>
        where order_code = #{orderCode} and pay_state = 1 and order_state =0;
    </select>
    <select id="selectOfflineOrderListTempCount" resultType="java.lang.Integer">
        select count(*) from ml_offline_order_temp ot
        <where>
            <if test="id != null">
                and ot.id = #{id}
            </if>
            <if test="merId != null">
                and ot.mer_id = #{merId}
            </if>
            <if test="buyerOpenid != null and buyerOpenid != ''">
                and ot.buyer_openid = #{buyerOpenid}
            </if>
            <if test="orderCode != null and orderCode != ''">
                and ot.order_code = #{orderCode}
            </if>
            <if test="account != null and account != ''">
                and ot.account = #{account}
            </if>
            <if test="payState != null">
                and ot.pay_state = #{payState}
            </if>
            <if test="orderState != null">
                and ot.order_state = #{orderState}
            </if>
            <if test="wsId != null and wsId != ''">
                and ot.ws_id = #{wsId}
            </if>
            <if test="mac != null and mac != ''">
                and ot.mac = #{mac}
            </if>
            <if test="ip != null and ip != ''">
                and ot.ip = #{ip}
            </if>
            <if test="spreadUid != null">
                and ot.spread_uid = #{spreadUid}
            </if>
            <if test="serviceProviderType != null">
                and ot.service_provider_type = #{serviceProviderType}
            </if>
            <if test="serviceProviderId != null">
                and ot.service_provider_id = #{serviceProviderId}
            </if>
            <if test="serviceProviderName != null and serviceProviderName != ''">
                and ot.service_provider_name = #{serviceProviderName}
            </if>
            <if test="startTime != null">
                and ot.create_time >= #{startTime}
            </if>
            <if test="endTime != null">
                and ot.create_time &lt;= #{endTime}
            </if>
        </where>
    </select>


    <!-- 新增线下订单 -->
    <insert id="insertOfflineOrder" parameterType="com.aggregate.payment.entity.OfflineOrder" useGeneratedKeys="true" keyProperty="id">
        insert into ml_offline_order
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="merId != null">mer_id,</if>
            <if test="orderCode != null and orderCode != ''">order_code,</if>
            <if test="commissionRate != null">commission_rate,</if>
            <if test="buyerOpenid != null and buyerOpenid != ''">buyer_openid,</if>
            <if test="account != null and account != ''">account,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="productDescription != null and productDescription != ''">product_description,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="payState != null">pay_state,</if>
            <if test="orderState != null">order_state,</if>
            <if test="commissionMer != null">commission_mer,</if>
            <if test="commissionPl != null">commission_pl,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="ip != null and ip != ''">ip,</if>
            <if test="mac != null and mac != ''">mac,</if>
            <if test="wsId != null and wsId != ''">ws_id,</if>
            <if test="bankUserCode != null and bankUserCode != ''">bank_user_code,</if>
            <if test="platBankCode != null and platBankCode != ''">plat_bank_code,</if>
            <if test="spreadUid != null">spread_uid,</if>
            <if test="uid != null">uid,</if>
            <if test="serviceProviderType != null">service_provider_type,</if>
            <if test="serviceProviderId != null">service_provider_id,</if>
            <if test="serviceProviderName != null and serviceProviderName != ''">service_provider_name,</if>
            <if test="mrchSno != null and mrchSno != ''">mrch_sno,</if>
            <if test="payType != null and payType != ''">pay_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="merId != null">#{merId},</if>
            <if test="orderCode != null and orderCode != ''">#{orderCode},</if>
            <if test="commissionRate != null">#{commissionRate},</if>
            <if test="buyerOpenid != null and buyerOpenid != ''">#{buyerOpenid},</if>
            <if test="account != null and account != ''">#{account},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="productDescription != null and productDescription != ''">#{productDescription},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="payState != null">#{payState},</if>
            <if test="orderState != null">#{orderState},</if>
            <if test="commissionMer != null">#{commissionMer},</if>
            <if test="commissionPl != null">#{commissionPl},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="ip != null and ip != ''">#{ip},</if>
            <if test="mac != null and mac != ''">#{mac},</if>
            <if test="wsId != null and wsId != ''">#{wsId},</if>
            <if test="bankUserCode != null and bankUserCode != ''">#{bankUserCode},</if>
            <if test="platBankCode != null and platBankCode != ''">#{platBankCode},</if>
            <if test="spreadUid != null">#{spreadUid},</if>
             <if test="uid != null">#{uid},</if>
            <if test="serviceProviderType != null">#{serviceProviderType},</if>
            <if test="serviceProviderId != null">#{serviceProviderId},</if>
            <if test="serviceProviderName != null and serviceProviderName != ''">#{serviceProviderName},</if>
            <if test="mrchSno != null and mrchSno != ''">#{mrchSno},</if>
            <if test="payType != null and payType != ''">#{payType},</if>
        </trim>
    </insert>
    <insert id="insertOfflineOrderTemp" parameterType="com.aggregate.payment.entity.OfflineOrder" >
        insert into ml_offline_order_temp
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="merId != null">mer_id,</if>
            <if test="orderCode != null and orderCode != ''">order_code,</if>
            <if test="commissionRate != null">commission_rate,</if>
            <if test="buyerOpenid != null and buyerOpenid != ''">buyer_openid,</if>
            <if test="account != null and account != ''">account,</if>
            <if test="productName != null and productName != ''">product_name,</if>
            <if test="productDescription != null and productDescription != ''">product_description,</if>
            <if test="paymentAmount != null">payment_amount,</if>
            <if test="payState != null">pay_state,</if>
            <if test="orderState != null">order_state,</if>
            <if test="commissionMer != null">commission_mer,</if>
            <if test="commissionPl != null">commission_pl,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="ip != null and ip != ''">ip,</if>
            <if test="mac != null and mac != ''">mac,</if>
            <if test="wsId != null and wsId != ''">ws_id,</if>
            <if test="bankUserCode != null and bankUserCode != ''">bank_user_code,</if>
            <if test="platBankCode != null and platBankCode != ''">plat_bank_code,</if>
            <if test="spreadUid != null">spread_uid,</if>
            <if test="uid != null">uid,</if>
            <if test="serviceProviderType != null">service_provider_type,</if>
            <if test="serviceProviderId != null">service_provider_id,</if>
            <if test="serviceProviderName != null and serviceProviderName != ''">service_provider_name,</if>
            <if test="mrchSno != null and mrchSno != ''">mrch_sno,</if>
            <if test="payType != null and payType != ''">pay_type,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="merId != null">#{merId},</if>
            <if test="orderCode != null and orderCode != ''">#{orderCode},</if>
            <if test="commissionRate != null">#{commissionRate},</if>
            <if test="buyerOpenid != null and buyerOpenid != ''">#{buyerOpenid},</if>
            <if test="account != null and account != ''">#{account},</if>
            <if test="productName != null and productName != ''">#{productName},</if>
            <if test="productDescription != null and productDescription != ''">#{productDescription},</if>
            <if test="paymentAmount != null">#{paymentAmount},</if>
            <if test="payState != null">#{payState},</if>
            <if test="orderState != null">#{orderState},</if>
            <if test="commissionMer != null">#{commissionMer},</if>
            <if test="commissionPl != null">#{commissionPl},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="ip != null and ip != ''">#{ip},</if>
            <if test="mac != null and mac != ''">#{mac},</if>
            <if test="wsId != null and wsId != ''">#{wsId},</if>
            <if test="bankUserCode != null and bankUserCode != ''">#{bankUserCode},</if>
            <if test="platBankCode != null and platBankCode != ''">#{platBankCode},</if>
            <if test="spreadUid != null">#{spreadUid},</if>
            <if test="uid != null">#{uid},</if>
            <if test="serviceProviderType != null">#{serviceProviderType},</if>
            <if test="serviceProviderId != null">#{serviceProviderId},</if>
            <if test="serviceProviderName != null and serviceProviderName != ''">#{serviceProviderName},</if>
            <if test="mrchSno != null and mrchSno != ''">#{mrchSno},</if>
            <if test="payType != null and payType != ''">#{payType},</if>
        </trim>
    </insert>

    <!-- 修改线下订单 -->
    <update id="updateOfflineOrder" parameterType="com.aggregate.payment.entity.OfflineOrder">
        update ml_offline_order
        <set>
            <if test="merId != null">mer_id = #{merId},</if>
            <if test="orderCode != null and orderCode != ''">order_code = #{orderCode},</if>
            <if test="commissionRate != null">commission_rate = #{commissionRate},</if>
            <if test="buyerOpenid != null and buyerOpenid != ''">buyer_openid = #{buyerOpenid},</if>
            <if test="account != null and account != ''">account = #{account},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="productDescription != null and productDescription != ''">product_description = #{productDescription},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="payState != null">pay_state = #{payState},</if>
            <if test="orderState != null">order_state = #{orderState},</if>
            <if test="commissionMer != null">commission_mer = #{commissionMer},</if>
            <if test="commissionPl != null">commission_pl = #{commissionPl},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="ip != null and ip != ''">ip = #{ip},</if>
            <if test="mac != null and mac != ''">mac = #{mac},</if>
            <if test="wsId != null and wsId != ''">ws_id = #{wsId},</if>
        </set>
        where order_code = #{orderCode}
    </update>
    <update id="updateOfflineOrderTemp" parameterType="com.aggregate.payment.entity.OfflineOrder">
        update ml_offline_order_temp
        <set>
            <if test="merId != null">mer_id = #{merId},</if>
            <if test="orderCode != null and orderCode != ''">order_code = #{orderCode},</if>
            <if test="commissionRate != null">commission_rate = #{commissionRate},</if>
            <if test="buyerOpenid != null and buyerOpenid != ''">buyer_openid = #{buyerOpenid},</if>
            <if test="account != null and account != ''">account = #{account},</if>
            <if test="productName != null and productName != ''">product_name = #{productName},</if>
            <if test="productDescription != null and productDescription != ''">product_description = #{productDescription},</if>
            <if test="paymentAmount != null">payment_amount = #{paymentAmount},</if>
            <if test="payState != null">pay_state = #{payState},</if>
            <if test="orderState != null">order_state = #{orderState},</if>
            <if test="commissionMer != null">commission_mer = #{commissionMer},</if>
            <if test="commissionPl != null">commission_pl = #{commissionPl},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="ip != null and ip != ''">ip = #{ip},</if>
            <if test="mac != null and mac != ''">mac = #{mac},</if>
            <if test="wsId != null and wsId != ''">ws_id = #{wsId},</if>
        </set>
        where order_code = #{orderCode}
    </update>
    <update id="updateOrderState">
        update ml_offline_order
        set order_state = 1
       where order_code IN
        <foreach collection="orderCodeList" item="orderCode" open="(" separator="," close=")">
            #{orderCode}
        </foreach>
    </update>
    <update id="updateErrStateIntTemp">
        update ml_offline_order_temp
        set order_state = 2
       where order_code IN
        <foreach collection="orderCodeList" item="orderCode" open="(" separator="," close=")">
            #{orderCode}
        </foreach>
    </update>
    <update id="updateErrStateInt">
        update ml_offline_order
        set order_state = 2
        where order_code IN
        <foreach collection="orderCodeList" item="orderCode" open="(" separator="," close=")">
            #{orderCode}
        </foreach>
    </update>
    <update id="refundOrderState">
        update ml_offline_order
        set order_state = 3
        where order_code = #{orderCode}
    </update>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aggregate.payment.mapper.MlOfflineSmsCodeMapper">

    <insert id="insertSmsCode" parameterType="com.aggregate.payment.entity.MlOfflineSmsCode">
        INSERT INTO ml_offline_sms_code (phone_number, sms_code,used)
        VALUES (#{phoneNumber}, #{smsCode},#{used})
    </insert>
    <update id="markUsed" parameterType="java.lang.Long">
        UPDATE ml_offline_sms_code
        SET used = 1
        WHERE id = #{id}
    </update>

    <select id="selectLatestByPhone" resultType="com.aggregate.payment.entity.MlOfflineSmsCode">
        SELECT id, phone_number, sms_code, create_time
        FROM ml_offline_sms_code
        WHERE phone_number = #{phoneNumber}
        ORDER BY create_time DESC
            LIMIT 1
    </select>
</mapper>
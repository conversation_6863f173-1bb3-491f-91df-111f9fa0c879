<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aggregate.payment.mapper.MlCameraInfoMapper">

    <!-- 批量插入 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO ml_camera_info
        (camera_index_code, camera_name, create_time, update_time, mer_id, visible)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.cameraIndexCode}, #{item.cameraName}, #{item.createTime}, #{item.updateTime}, #{item.merId}, #{item.visible})
        </foreach>
    </insert>

    <!-- 批量更新（只演示常用字段，实际可按需扩展） -->
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE ml_camera_info
            <set>
                <if test="item.cameraIndexCode != null and item.cameraIndexCode != ''">camera_index_code = #{item.cameraIndexCode},</if>
                <if test="item.cameraName != null and item.cameraName != ''">camera_name = #{item.cameraName},</if>
                <if test="item.createTime != null">create_time = #{item.createTime},</if>
                <if test="item.updateTime != null">update_time = #{item.updateTime},</if>
                <if test="item.merId != null">mer_id = #{item.merId},</if>
                <if test="item.visible != null">visible = #{item.visible},</if>
            </set>

            WHERE id = #{item.id}
        </foreach>
    </update>
    <update id="upDateDelFlag" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE ml_camera_info
            set  del_flag = 1
            WHERE id = #{item.id}
        </foreach>
    </update>

    <!-- 批量删除 -->
    <delete id="deleteBatchByIds" parameterType="java.util.List">
        DELETE FROM ml_camera_info WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 批量查询 -->
    <select id="selectBatchByIds" parameterType="java.util.List" resultType="com.aggregate.payment.entity.MlCameraInfo">
        SELECT id, camera_index_code, camera_name, create_time, update_time, mer_id, visible
        FROM ml_camera_info
        WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>

    <!-- 分页条件查询 -->
    <select id="selectByCondition" resultType="com.aggregate.payment.entity.MlCameraInfo">
        SELECT id, camera_index_code, camera_name, create_time, update_time, mer_id, visible
        FROM ml_camera_info
        <where>
            <if test="merId != null">
                AND mer_id = #{merId}
            </if>
            <if test="visible != null">
                AND visible = #{visible}
            </if>
            <if test="cameraName != null and cameraName != ''">
                AND camera_name LIKE CONCAT('%', #{cameraName}, '%')
            </if>
        </where>
        ORDER BY create_time DESC
        <if test="pageNum != null and pageSize != null">
        LIMIT #{pageSize} OFFSET #{pageNum}
        </if>
    </select>

    <!-- 条件计数 -->
    <select id="countByCondition" resultType="int">
        SELECT COUNT(1)
        FROM ml_camera_info
        <where>
            <if test="merId != null">
                AND mer_id = #{merId}
            </if>
            <if test="visible != null">
                AND visible = #{visible}
            </if>
            <if test="cameraName != null and cameraName != ''">
                AND camera_name LIKE CONCAT('%', #{cameraName}, '%')
            </if>
        </where>
    </select>
    <select id="selectById" resultType="com.aggregate.payment.entity.MlCameraInfo">
        SELECT id, camera_index_code, camera_name, create_time, update_time, mer_id, visible
        FROM ml_camera_info
        WHERE id = #{id}
    </select>

</mapper> 
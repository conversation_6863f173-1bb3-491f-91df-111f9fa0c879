<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aggregate.payment.mapper.pointsRecordMapper">

    <resultMap type="com.aggregate.payment.entity.PointsRecord" id="pointsRecordResult">
        <id property="id" column="id"/>
        <result property="description" column="description"/>
        <result property="points" column="points"/>
        <result property="orderNo" column="order_no"/>
        <result property="type" column="type"/>
        <result property="userId" column="user_id"/>
        <result property="status" column="status"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="userType" column="user_type"/>
        <result property="totalPeriods" column="total_periods"/>
        <result property="dateChar" column="date_char"/>
        <result property="invitorTag" column="invitor_tag"/>
        <result property="orderType" column="order_type"/>

    </resultMap>

    <sql id="selectpointsRecordVo">
        select id, description, points, order_no, type, user_id, status, create_time, update_time, user_type,total_periods
        from ml_points_record
    </sql>

    <!-- 批量插入 -->
    <insert id="insertList" parameterType="java.util.List">
        insert into ml_points_record(description, points, order_no, type, user_id, status, create_time, update_time, user_type,total_periods,date_char,invitor_tag,order_type)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.description}, #{item.points}, #{item.orderNo}, #{item.type}, #{item.userId}, #{item.status}, #{item.createTime}, #{item.updateTime}, #{item.userType},#{item.totalPeriods},#{item.dateChar},#{item.invitorTag},#{item.orderType})
        </foreach>
    </insert>

    <!-- 查询积分记录列表 -->
    <select id="selectList" parameterType="com.aggregate.payment.entity.PointsRecord" resultMap="pointsRecordResult">
        <include refid="selectpointsRecordVo"/>
        <where>
            <if test="description != null and description != ''">
                and description like concat('%', #{description}, '%')
            </if>
            <if test="orderNo != null and orderNo != ''">
                and order_no = #{orderNo}
            </if>
            <if test="type != null">
                and type = #{type}
            </if>
            <if test="userId != null and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="status != null">
                and status = #{status}
            </if>
            <if test="userType != null">
                and user_type = #{userType}
            </if>
        </where>
        order by create_time desc
    </select>

    <!-- 根据ID更新 -->
    <update id="updateById" parameterType="com.aggregate.payment.entity.PointsRecord">
        update ml_points_record
        <set>
            <if test="description != null and description != ''">description = #{description},</if>
            <if test="points != null">points = #{points},</if>
            <if test="orderNo != null and orderNo != ''">order_no = #{orderNo},</if>
            <if test="type != null">type = #{type},</if>
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="userType != null">user_type = #{userType},</if>
        </set>
        where id = #{id}
    </update>

    <!-- 批量更新 -->
    <update id="updateList" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update ml_points_record
            <set>
                <if test="item.description != null and item.description != ''">description = #{item.description},</if>
                <if test="item.points != null">points = #{item.points},</if>
                <if test="item.orderNo != null and item.orderNo != ''">order_no = #{item.orderNo},</if>
                <if test="item.type != null">type = #{item.type},</if>
                <if test="item.userId != null and item.userId != ''">user_id = #{item.userId},</if>
                <if test="item.status != null">status = #{item.status},</if>
                <if test="item.updateTime != null">update_time = #{item.updateTime},</if>
                <if test="item.userType != null">user_type = #{item.userType},</if>
            </set>
            where id = #{item.id}
        </foreach>
    </update>
    <!-- 查询用户积分汇总统计 -->
    <select id="selectUserpointsSummary" parameterType="com.aggregate.payment.entity.PointsRecord" resultType="java.util.Map">
        SELECT
        -- 总获得积分（新增类型的积分总和）
        COALESCE(SUM(CASE WHEN type = 1 THEN points ELSE 0 END), 0) AS totalEarned,

        -- 已兑换积分（扣除类型且已结算的积分总和）
        COALESCE(SUM(CASE WHEN type = 0 AND status = 1 THEN ABS(points) ELSE 0 END), 0) AS totalExchanged,

        -- 未兑换积分（总获得 - 已兑换）
        COALESCE(SUM(CASE WHEN type = 1 THEN points ELSE 0 END), 0) -
        COALESCE(SUM(CASE WHEN type = 0 AND status = 1 THEN ABS(points) ELSE 0 END), 0) AS totalAvailable,

        -- 冻结积分（扣除类型且未结算的积分总和）
        COALESCE(SUM(CASE WHEN type = 0 AND status = 0 THEN ABS(points) ELSE 0 END), 0) AS totalFrozen

        FROM ml_points_record
        WHERE user_id = #{userId}
        <if test="userType != null">
            AND user_type = #{userType}
        </if>
    </select>
    <!-- 查询用户积分明细列表 -->
    <select id="selectUserpointsDetails" parameterType="com.aggregate.payment.entity.PointsRecord" resultMap="pointsRecordResult">
        SELECT
        id, description, points, order_no, type, user_id, status,
        create_time, update_time, user_type
        FROM ml_points_record
        <where>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="type != null">
                AND type = #{type}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="userType != null">
                AND user_type = #{userType}
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND order_no = #{orderNo}
            </if>
        </where>
        ORDER BY create_time DESC
        <if test="pageNum != null and pageSize != null">
            LIMIT #{pageSize} OFFSET #{pageNum}
        </if>
    </select>
    <select id="ifExist" resultType="java.lang.Integer">
        select count(*) from ml_points_record where order_no = #{orderNo}
    </select>
    <select id="selectUserpointsDetailsCount" resultType="java.lang.Integer"
            parameterType="com.aggregate.payment.entity.PointsRecord">
        SELECT COUNT(*) FROM ml_points_record
        <where>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="type != null">
                AND type = #{type}
            </if>
            <if test="status != null">
                AND status = #{status}
            </if>
            <if test="userType != null">
                AND user_type = #{userType}
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND order_no = #{orderNo}
            </if>
        </where>
    </select>
    <select id="selectPointsRecordList" resultType="com.aggregate.payment.entity.pointsSearch.PointsRecordVo"
            parameterType="com.aggregate.payment.entity.pointsSearch.PointsRecordQuery">
        SELECT
        p.points,
        p.order_no,
        p.type,
        p.user_id,
        p.user_type,
        p.order_type,
        p.invitor_tag,
        p.create_time,
        p.description,

        -- 获取订单金额（线下或线上）
        CASE
        WHEN p.order_type = 0 THEN o1.payment_amount
        ELSE o2.pay_price
        END AS order_amount,

        -- 获取订单账户
        CASE
        WHEN p.order_type = 0 THEN o1.account
        ELSE NULL
        END AS order_account,

        -- 获取订单 mer_id
        CASE
        WHEN p.order_type = 0 THEN o1.mer_id
        ELSE o2.mer_id
        END AS mer_id,

        -- 商户名
        m.mer_name,
        -- 商户分佣率（来自不同表也可以合并）
        CASE
        WHEN p.order_type = 0 THEN m.commission_rate
        ELSE o2.commission_rate
        END AS commission_rate,

        -- 用户/商户账号（根据 user_type）
        CASE
        WHEN p.user_type = 0 THEN u.account
        ELSE mu.account
        END AS user_account

        FROM ml_points_record p

        -- 线下订单（order_type = 0）
        LEFT JOIN ml_offline_order o1 ON p.order_type = 0 AND p.order_no = o1.order_code

        -- 线上订单（order_type = 1）
        LEFT JOIN ml_store_order o2 ON p.order_type = 1 AND p.order_no = o2.order_sn

        -- 商户信息（不论线上线下都需要）
        LEFT JOIN ml_merchant m ON
        (p.order_type = 0 AND o1.mer_id = m.mer_id)
        OR (p.order_type = 1 AND o2.mer_id = m.mer_id)

        -- 用户表（user_type = 0）
        LEFT JOIN ml_user u ON p.user_type = 0 AND p.user_id = u.uid

        -- 商户表（user_type = 1）
        LEFT JOIN ml_merchant_admin mu ON p.user_type = 1 AND p.user_id = mu.mer_id
        <where>
            <if test="userId != null">
                AND p.user_id = #{userId}
            </if>
            <if test="userType != null">
                AND p.user_type = #{userType}
            </if>
            <if test="orderType != null">
                AND p.order_type = #{orderType}
            </if>
            <if test="orderNo != null and orderNo != ''">
                AND p.order_no = #{orderNo}
            </if>
            <if test="beginTime != null and beginTime != ''">
                AND p.create_time &gt;= #{beginTime}
            </if>
            <if test="endTime != null and endTime != ''">
                AND p.create_time &lt;= #{endTime}
            </if>
        </where>

        ORDER BY p.create_time DESC

        LIMIT #{pageSize} OFFSET #{offset}
    </select>
    <select id="selectPointsRecordListCount" resultType="java.lang.Integer"
            parameterType="com.aggregate.payment.entity.pointsSearch.PointsRecordQuery">
            SELECT COUNT(*)
            FROM ml_points_record p
            -- 根据 order_type 判断关联不同的订单表
            LEFT JOIN ml_offline_order o1 ON p.order_type = 0 AND p.order_no = o1.order_code
            LEFT JOIN ml_store_order o2 ON p.order_type = 1 AND p.order_no = o2.order_sn
            -- 商户信息
            LEFT JOIN ml_merchant m ON
            (p.order_type = 0 AND o1.mer_id = m.mer_id)
            OR
            (p.order_type = 1 AND o2.mer_id = m.mer_id)
            -- 用户信息（user_type 决定关联用户或商户）
            LEFT JOIN ml_user u ON p.user_type = 0 AND p.user_id = u.uid
            LEFT JOIN ml_merchant mu ON p.user_type = 1 AND p.user_id = mu.mer_id

            <where>
                <if test="userId != null">
                    AND p.user_id = #{userId}
                </if>
                <if test="userType != null">
                    AND p.user_type = #{userType}
                </if>
                <if test="orderType != null">
                    AND p.order_type = #{orderType}
                </if>
                <if test="orderNo != null and orderNo != ''">
                    AND p.order_no = #{orderNo}
                </if>
                <if test="beginTime != null and beginTime != ''">
                    AND p.create_time &gt;= #{beginTime}
                </if>
                <if test="endTime != null and endTime != ''">
                    AND p.create_time &lt;= #{endTime}
                </if>
            </where>



    </select>

</mapper>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aggregate.payment.mapper.MlStoreOrderMapper">
    <resultMap id="OrderBaseInfoResult" type="com.aggregate.payment.entity.OrderBaseInfo">
        <result property="orderId" column="order_id"/>
        <result property="uid" column="uid"/>
        <result property="merId" column="mer_id"/>
        <result property="payPrice" column="pay_price"/>
        <result property="orderSn" column="order_sn"/>
        <result property="commissionRate" column="commission_rate"/>
    </resultMap>
    <select id="selectOrderBaseInfo" resultMap="OrderBaseInfoResult">
        select order_id,uid,mer_id,pay_price,order_sn,commission_rate from ml_store_order where order_id in
                    <foreach collection="orderIds" item="id" open="(" separator="," close=")">
                        #{id}
                    </foreach>
    </select>
</mapper>
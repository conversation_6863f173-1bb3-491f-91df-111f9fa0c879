<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aggregate.payment.mapper.MlCameraProductRelMapper">

    <!-- 批量插入 -->
    <insert id="insertBatch" parameterType="java.util.List">
        INSERT INTO ml_camera_product_rel
        (camera_id, product_id, mer_id)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.cameraId}, #{item.productId}, #{item.merId})
        </foreach>
    </insert>

    <!-- 批量更新 -->
    <update id="updateBatch" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            UPDATE ml_camera_product_rel
            SET
                camera_id = #{item.cameraId},
                product_id = #{item.productId},
                mer_id = #{item.merId},
                delflag = #{item.delflag},
                create_time = #{item.createTime},
                update_time = #{item.updateTime}
            WHERE id = #{item.id}
        </foreach>
    </update>
    <update id="unBind">
        <foreach collection="list" item="item" separator=";">
            UPDATE ml_camera_product_rel
            SET
                delflag = 1
            <where>
                <if test="item.cameraId != null">AND camera_id = #{item.cameraId}</if>
                <if test="item.productId != null">AND product_id = #{item.productId}</if>
                <if test="item.merId != null">AND mer_id = #{item.merId}</if>
            </where>
        </foreach>
    </update>

    <!-- 批量删除 -->
    <delete id="deleteBatchByIds" parameterType="java.util.List">
        DELETE FROM ml_camera_product_rel WHERE id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <!-- 条件查询 -->
    <select id="selectByCondition" resultType="com.aggregate.payment.entity.MlCameraProductRel">
        SELECT id, camera_id, product_id, mer_id, delflag, create_time, update_time
        FROM ml_camera_product_rel
        <where>
            <if test="cameraId != null">AND camera_id = #{cameraId}</if>
            <if test="productId != null">AND product_id = #{productId}</if>
            <if test="merId != null">AND mer_id = #{merId}</if>
            <if test="delflag != null">AND delflag = #{delflag}</if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!-- 查询某摄像机当前有效(未删除)的绑定记录 -->
    <select id="selectValidByCamera" resultType="com.aggregate.payment.entity.MlCameraProductRel">
        SELECT id, camera_id, product_id, mer_id, delflag, create_time, update_time
        FROM ml_camera_product_rel
        WHERE camera_id = #{cameraId}
          AND mer_id   = #{merId}
          AND delflag  = 0
    </select>

    <!-- 单条插入 -->
    <insert id="insertSingle" parameterType="com.aggregate.payment.entity.MlCameraProductRel">
        INSERT INTO ml_camera_product_rel
        (camera_id, product_id, mer_id, delflag, create_time, update_time)
        VALUES
        (#{cameraId}, #{productId}, #{merId}, #{delflag}, now(), now())
    </insert>

    <!-- 单条更新 -->
    <update id="updateSingle" parameterType="com.aggregate.payment.entity.MlCameraProductRel">
        UPDATE ml_camera_product_rel
        <set>
            <if test="cameraId != null">camera_id = #{cameraId},</if>
            <if test="productId != null">product_id = #{productId},</if>
            <if test="merId != null">mer_id = #{merId},</if>
            <if test="delflag != null">delflag = #{delflag},</if>
            update_time = now()
        </set>
        WHERE id = #{id}
    </update>

    <!-- 单条逻辑删除 -->
    <update id="deleteSingle" parameterType="com.aggregate.payment.entity.MlCameraProductRel">
        UPDATE ml_camera_product_rel
        SET delflag = 1, update_time = now()
        <where>
            <if test="id != null">AND id = #{id}</if>
            <if test="cameraId != null">AND camera_id = #{cameraId}</if>
            <if test="productId != null">AND product_id = #{productId}</if>
            <if test="merId != null">AND mer_id = #{merId}</if>
        </where>
    </update>

    <!-- 查询单条 -->
    <select id="selectOne" resultType="com.aggregate.payment.entity.MlCameraProductRel">
        SELECT id, camera_id, product_id, mer_id, delflag, create_time, update_time
        FROM ml_camera_product_rel
        <where>
            <if test="id != null">AND id = #{id}</if>
            <if test="cameraId != null">AND camera_id = #{cameraId}</if>
            <if test="productId != null">AND product_id = #{productId}</if>
            <if test="merId != null">AND mer_id = #{merId}</if>
        </where>
        LIMIT 1
    </select>

</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aggregate.payment.mapper.OfflineUserMapper">
    <sql id="select">
        select us.id,us.open_id,us.phone,us.create_time,us.update_time from ml_offline_user us
    </sql>

    <insert id="insertUserScore">
        insert into ml_offline_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null and openId != ''">
                open_id,
            </if>
            <if test="phone != null and phone != ''">
                phone,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openId != null and openId != ''">
                #{openId},
            </if>
            <if test="phone != null and phone != ''">
                #{phone},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
        </trim>
    </insert>
    <update id="updateUserScore">
        update ml_offline_user
        <set>
            <if test="phone != null and phone != ''">
                phone = #{phone},
            </if>
        </set>
        <where>
            <if test="id != null and id != ''">
                and id = #{id}
            </if>
            <if test="openId != null and openId != ''">
                and open_id = #{openId}
            </if>
        </where>
    </update>
    <select id="selectUserScoreList" resultType="com.aggregate.payment.entity.OfflineUser"
            parameterType="com.aggregate.payment.entity.OfflineUser">
        <include refid="select"/>
        <where>
             <if test="id != null and id != ''">
                and us.id = #{id}
            </if>
            <if test="openId != null and openId != ''">
                and us.open_id = #{openId}
            </if>
            <if test="phone != null and phone != ''">
                and us.phone = #{phone}
            </if>

        </where>
        order by us.update_time desc
    </select>
</mapper>
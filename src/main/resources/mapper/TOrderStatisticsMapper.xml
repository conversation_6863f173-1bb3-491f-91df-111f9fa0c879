<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aggregate.payment.mapper.TOrderStatisticsMapper">

    <insert id="insertBatch" parameterType="com.aggregate.payment.entity.TOrderStatistics">
        INSERT INTO t_order_statistics (merchant_id, amount, type, `timestamp`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.merchantId}, #{item.amount}, #{item.type}, #{item.timestamp})
        </foreach>
    </insert>

</mapper> 
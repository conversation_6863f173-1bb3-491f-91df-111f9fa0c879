<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aggregate.payment.mapper.MerchantpointsMapper">

    <select id="selectUserpoints" resultType="com.aggregate.payment.entity.MerchantPoints">
          select points ,commission_rate ,mer_name,bank_user_code,business_license,mer_id from ml_merchant where mer_id = #{merId}
    </select>
    <update id="updateMlpoints">
          update ml_merchant set points = points + #{points} where mer_id = #{merId}
    </update>
    <update id="batchUpdateMerchantPoints" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update ml_merchant
            set points = points + #{item.points}
            where mer_id = #{item.merId}
        </foreach>
    </update>
    <update id="batchUpdateMerchantPointsGetCount" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update ml_merchant
            set points_get_count = points_get_count + #{item.pointsGetCount}
            where mer_id = #{item.merId}
        </foreach>
    </update>
</mapper>
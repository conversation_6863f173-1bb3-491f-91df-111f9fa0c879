<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aggregate.payment.mapper.MlUserMapper">
    <insert id="insert" parameterType="com.aggregate.payment.entity.Userpoints">
        insert into ml_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="spreadUid != null">spread_uid,</if>
            <if test="account != null and account != ''">account,</if>
            <if test="pwd != null and pwd != ''">pwd,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="nickname != null and nickname != ''">nickname,</if>
            <if test="avatar != null and avatar != ''">avatar,</if>
            <if test="lastIp != null and lastIp != ''">last_ip,</if>
            <if test="userType != null and userType != ''">user_type,</if>
            <if test="serviceProviderType != null and serviceProviderType != ''">service_provider_type,</if>
            <if test="serviceProviderId != null and serviceProviderId != ''">service_provider_id,</if>
            <if test="serviceProviderName != null and serviceProviderName != ''">service_provider_name,</if>
        </trim>
        values
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="spreadUid != null">#{spreadUid},</if>
            <if test="account != null and account != ''">#{account},</if>
            <if test="pwd != null and pwd != ''">#{pwd},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="nickname != null and nickname != ''">#{nickname},</if>
            <if test="avatar != null and avatar != ''">#{avatar},</if>
            <if test="lastIp != null and lastIp != ''">#{lastIp},</if>
            <if test="userType != null and userType != ''">#{userType},</if>
            <if test="serviceProviderType != null and serviceProviderType != ''">#{serviceProviderType},</if>
            <if test="serviceProviderId != null and serviceProviderId != ''">#{serviceProviderId},</if>
            <if test="serviceProviderName != null and serviceProviderName != ''">#{serviceProviderName},</if>
        </trim>

    </insert>

    <select id="selectMlUser" resultType="com.aggregate.payment.entity.Userpoints">
        select spread_uid,uid,points,account,service_provider_type,service_provider_id,service_provider_name from ml_user where uid = #{uid}
    </select>
    <select id="selectMlUserE" resultType="com.aggregate.payment.entity.Userpoints">
        select spread_uid,uid,points,account from ml_user
        <where>
            <if test="uid != null and uid != ''">
                and uid = #{uid}
            </if>
            <if test="account != null and account != ''">
                and account = #{account}
            </if>
        </where>
    </select>
    <select id="selectUsers" resultType="com.aggregate.payment.entity.Userpoints">
        select spread_uid,uid,points,account from ml_user where account IN
            <foreach collection="accounts" item="account" open="(" separator="," close=")">
                #{account}
            </foreach>
    </select>
    <select id="selectMlUserByAccount" resultType="com.aggregate.payment.entity.Userpoints"
            parameterType="java.lang.String">
        select spread_uid,uid,points,account,service_provider_type,service_provider_id,service_provider_name from ml_user where account = #{account}
    </select>
    <update id="updateMlUser">
        update ml_user set points = points + #{points} where uid = #{uid}
    </update>
    <update id="batchUpdateMlUsers" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update ml_user
            set points = points + #{item.points}
            where uid = #{item.uid}
        </foreach>
    </update>
    <update id="batchUpdateUserGetPoints" parameterType="java.util.List">
        <foreach collection="list" item="item" separator=";">
            update ml_user
            set points_get_count = points_get_count + #{item.pointsGetCount}
            where uid = #{item.uid}
        </foreach>
    </update>
</mapper>
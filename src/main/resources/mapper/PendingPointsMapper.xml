<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aggregate.payment.mapper.PendingPointsMapper">
    <insert id="BatchInsertPendingPoints" parameterType="java.util.List">
     insert into t_pending_points (user_id, user_type, points, batch_no, period_num, create_time)
     values
         <foreach collection="list" item="item" separator=",">
            (#{item.userId}, #{item.userType}, #{item.points}, #{item.batchNo}, #{item.periodNum}, #{item.createTime})
        </foreach>
    </insert>
</mapper>
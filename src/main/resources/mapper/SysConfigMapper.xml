<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.aggregate.payment.mapper.SysConfigMapper">

    <resultMap id="SysConfigResult" type="com.aggregate.payment.entity.SysConfig">
        <id property="id" column="id"/>
        <result property="code" column="code"/>
        <result property="value" column="value"/>
        <result property="desc" column="desc"/>
    </resultMap>

    <select id="selectValueByCode" resultType="java.lang.String">
        select value from t_sys_config where code = #{code}
    </select>

    <select id="selectByCode" resultMap="SysConfigResult">
        select id, code, value, `desc` from t_sys_config where code = #{code}
    </select>

</mapper>
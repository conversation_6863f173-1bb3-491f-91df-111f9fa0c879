-- 积分记录表
DROP TABLE IF EXISTS `merchant_points`;
CREATE TABLE `merchant_points` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  `description` VARCHAR(200) NOT NULL COMMENT '积分说明',
  `points` DECIMAL(10,2) NOT NULL COMMENT '积分数量',
  `order_no` VARCHAR(100) COMMENT '订单编号',
  `type` TINYINT NOT NULL COMMENT '类型(1-新增, 2-扣除)',
  `user_id` VARCHAR(100) NOT NULL COMMENT '用户ID',
  `status` TINYINT DEFAULT 0 COMMENT '状态(0-未结算, 1-已结算)',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 索引
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_order_no` (`order_no`),
  INDEX `idx_type` (`type`),
  INDEX `idx_status` (`status`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分记录表';

-- 插入测试数据
INSERT INTO `merchant_points` (`description`, `points`, `order_no`, `type`, `user_id`, `status`) VALUES
('订单支付获得积分', 100.00, 'ORDER_001', 1, 'USER_001', 0),
('积分兑换扣除', -50.00, 'ORDER_002', 2, 'USER_001', 1),
('推荐奖励积分', 20.00, 'ORDER_003', 1, 'USER_002', 0);

-- 聚合支付数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS aggregate_payment DEFAULT CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

USE aggregate_payment;

-- 商户表
CREATE TABLE merchant (
    merchant_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '商户ID',
    merchant_code VARCHAR(50) NOT NULL UNIQUE COMMENT '商户编号',
    merchant_name VARCHAR(100) NOT NULL COMMENT '商户名称',
    contact_person VARCHAR(50) COMMENT '联系人',
    contact_phone VARCHAR(20) COMMENT '联系电话',
    merchant_address VARCHAR(200) COMMENT '商户地址',
    status CHAR(1) DEFAULT '0' COMMENT '商户状态 (0正常 1停用)',
    service_fee_rate DECIMAL(5,4) DEFAULT 0.0060 COMMENT '平台服务费率',
    wechat_status CHAR(1) DEFAULT '0' COMMENT '微信支付配置状态 (0未配置 1已配置)',
    alipay_status CHAR(1) DEFAULT '0' COMMENT '支付宝支付配置状态 (0未配置 1已配置)',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建者',
    update_by VARCHAR(50) COMMENT '更新者',
    remark VARCHAR(500) COMMENT '备注'
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='商户表';

-- 聚合二维码表
CREATE TABLE aggregate_qr_code (
    qr_code_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '二维码ID',
    qr_code_key VARCHAR(100) NOT NULL UNIQUE COMMENT '二维码唯一标识',
    merchant_id BIGINT NOT NULL COMMENT '商户ID',
    merchant_code VARCHAR(50) NOT NULL COMMENT '商户编号',
    qr_code_type CHAR(1) DEFAULT '2' COMMENT '二维码类型 (1固定金额 2动态金额)',
    fixed_amount DECIMAL(10,2) COMMENT '固定金额',
    service_fee_rate DECIMAL(5,4) NOT NULL COMMENT '服务费率',
    qr_code_url VARCHAR(500) NOT NULL COMMENT '二维码URL',
    qr_code_image_path VARCHAR(200) COMMENT '二维码图片路径',
    status CHAR(1) DEFAULT '0' COMMENT '二维码状态 (0正常 1停用)',
    expire_time DATETIME COMMENT '过期时间',
    scan_count INT DEFAULT 0 COMMENT '扫码次数',
    last_scan_time DATETIME COMMENT '最后扫码时间',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    create_by VARCHAR(50) COMMENT '创建者',
    update_by VARCHAR(50) COMMENT '更新者',
    remark VARCHAR(500) COMMENT '备注',
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_merchant_code (merchant_code),
    INDEX idx_qr_code_key (qr_code_key)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='聚合二维码表';

-- 支付记录表
CREATE TABLE payment_record (
    record_id BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '支付记录ID',
    payment_order_no VARCHAR(50) NOT NULL UNIQUE COMMENT '支付订单号',
    merchant_id BIGINT NOT NULL COMMENT '商户ID',
    merchant_code VARCHAR(50) NOT NULL COMMENT '商户编号',
    qr_code_key VARCHAR(100) NOT NULL COMMENT '二维码标识',
    payment_type VARCHAR(20) NOT NULL COMMENT '支付方式 (WECHAT微信 ALIPAY支付宝)',
    user_id VARCHAR(100) NOT NULL COMMENT '用户标识 (微信openid或支付宝用户ID)',
    payment_amount DECIMAL(10,2) NOT NULL COMMENT '支付金额',
    service_fee DECIMAL(10,2) NOT NULL COMMENT '服务费',
    merchant_amount DECIMAL(10,2) NOT NULL COMMENT '商户实收金额',
    payment_status CHAR(1) DEFAULT '0' COMMENT '支付状态 (0待支付 1支付成功 2支付失败 3已退款)',
    third_party_trade_no VARCHAR(100) COMMENT '第三方交易号',
    payment_time DATETIME COMMENT '支付完成时间',
    user_ip VARCHAR(50) COMMENT '用户IP',
    user_agent VARCHAR(500) COMMENT '用户设备信息',
    del_flag CHAR(1) DEFAULT '0' COMMENT '删除标志 (0代表存在 1代表删除)',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) COMMENT '备注',
    INDEX idx_merchant_id (merchant_id),
    INDEX idx_payment_order_no (payment_order_no),
    INDEX idx_qr_code_key (qr_code_key),
    INDEX idx_user_id (user_id),
    INDEX idx_payment_time (payment_time)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='支付记录表';

-- 插入测试数据
INSERT INTO merchant (merchant_code, merchant_name, contact_person, contact_phone, service_fee_rate, create_by) 
VALUES 
('MERCHANT_001', '测试商户1', '张三', '13800138001', 0.0060, 'system'),
('MERCHANT_002', '测试商户2', '李四', '13800138002', 0.0080, 'system');

-- 插入测试二维码
INSERT INTO aggregate_qr_code (qr_code_key, merchant_id, merchant_code, service_fee_rate, qr_code_url, create_by)
VALUES 
('MERCHANT_001_TEST_001', 1, 'MERCHANT_001', 0.0060, 'http://localhost:8081/aggregate-pay/scan?code=MERCHANT_001_TEST_001', 'system'),
('MERCHANT_002_TEST_001', 2, 'MERCHANT_002', 0.0080, 'http://localhost:8081/aggregate-pay/scan?code=MERCHANT_002_TEST_001', 'system');

-- 积分流水表
DROP TABLE IF EXISTS `score_record`;
CREATE TABLE `score_record` (
  `id` BIGINT AUTO_INCREMENT PRIMARY KEY COMMENT '主键ID',
  `description` VARCHAR(200) NOT NULL COMMENT '积分说明',
  `score_amount` DECIMAL(10,2) NOT NULL COMMENT '积分数量',
  `order_no` VARCHAR(100) COMMENT '订单编号',
  `score_type` TINYINT NOT NULL COMMENT '积分类型(1-新增, 2-扣除)',
  `user_id` VARCHAR(100) NOT NULL COMMENT '用户ID',
  `user_type` TINYINT NOT NULL COMMENT '用户类型(1-B端商户, 2-C端消费者, 3-C端推荐人)',
  `settle_status` TINYINT DEFAULT 0 COMMENT '结算状态(0-未结算, 1-已结算)',
  
  -- 业务关联字段
  `business_type` VARCHAR(50) COMMENT '业务类型(PAYMENT-支付获得积分)',
  `business_id` VARCHAR(100) COMMENT '关联业务ID',
  `qr_code_key` VARCHAR(100) COMMENT '二维码标识',
  
  -- 积分计算相关
  `order_amount` DECIMAL(10,2) COMMENT '订单实付金额',
  `commission_rate` DECIMAL(5,4) COMMENT '分佣比例',
  `score_coefficient` DECIMAL(5,2) COMMENT '积分系数',
  `referrer_rate` DECIMAL(5,4) COMMENT '推荐人比例',
  
  -- 关联用户
  `merchant_id` VARCHAR(100) COMMENT 'B端商户ID',
  `consumer_id` VARCHAR(100) COMMENT 'C端消费者ID', 
  `referrer_id` VARCHAR(100) COMMENT 'C端推荐人ID',
  
  -- 时间字段
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  -- 其他字段
  `remark` VARCHAR(500) COMMENT '备注',
  `del_flag` TINYINT DEFAULT 0 COMMENT '删除标志(0-存在, 1-删除)',
  
  -- 索引
  INDEX `idx_user_id` (`user_id`),
  INDEX `idx_order_no` (`order_no`),
  INDEX `idx_business` (`business_type`, `business_id`),
  INDEX `idx_create_time` (`create_time`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='积分流水表';

-- 用户积分汇总表
DROP TABLE IF EXISTS `user_score_summary`;
CREATE TABLE `user_score_summary` (
  `user_id` VARCHAR(100) PRIMARY KEY COMMENT '用户ID',
  `user_type` TINYINT NOT NULL COMMENT '用户类型(1-B端商户, 2-C端消费者, 3-C端推荐人)',
  `total_score` DECIMAL(10,2) DEFAULT 0 COMMENT '总积分',
  `available_score` DECIMAL(10,2) DEFAULT 0 COMMENT '可用积分',
  `frozen_score` DECIMAL(10,2) DEFAULT 0 COMMENT '冻结积分',
  `consumed_score` DECIMAL(10,2) DEFAULT 0 COMMENT '已消费积分',
  `create_time` DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `update_time` DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  
  INDEX `idx_user_type` (`user_type`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='用户积分汇总表';

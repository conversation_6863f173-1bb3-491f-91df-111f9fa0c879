package com.aggregate.payment.mapper;


import com.aggregate.payment.entity.OfflineUser;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

@Mapper
public interface OfflineUserMapper extends BaseMapper<OfflineUser> {
    List<OfflineUser> selectUserScoreList(OfflineUser userScore);
    int insertUserScore(OfflineUser userScore);
    int updateUserScore(OfflineUser userScore);
}

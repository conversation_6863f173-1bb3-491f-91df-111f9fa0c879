package com.aggregate.payment.mapper;

import com.aggregate.payment.entity.OfflineOrder;
import com.aggregate.payment.entity.pointsSearch.PointsRecordQuery;
import com.aggregate.payment.entity.pointsSearch.PointsRecordVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 线下订单Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface OfflineOrderMapper extends BaseMapper<OfflineOrder> {
    
    /**
     * 查询线下订单列表
     * 
     * @param offlineOrder 查询条件
     * @return 线下订单列表
     */
    List<OfflineOrder> selectOfflineOrderList(OfflineOrder offlineOrder);
    List<OfflineOrder> selectOfflineOrderListTemp(OfflineOrder offlineOrder);
    int selectOfflineOrderListTempCount(OfflineOrder offlineOrder);
    List<OfflineOrder> selectOfflineOrderList4Pay(OfflineOrder offlineOrder);
    int selectOfflineOrderCount(OfflineOrder offlineOrder);
    /**
     * 根据ID查询线下订单
     * 
     * @param id 订单ID
     * @return 线下订单信息
     */
    OfflineOrder selectOfflineOrderById(@Param("id") Long id);
//    OfflineOrder selectOfflineOrderByIdList(@Param("id") Long ids);
    /**
     * 新增线下订单
     * 
     * @param offlineOrder 线下订单信息
     * @return 影响行数
     */
    int insertOfflineOrder(OfflineOrder offlineOrder);
    int insertOfflineOrderTemp(OfflineOrder offlineOrder);
    
    /**
     * 修改线下订单
     * 
     * @param offlineOrder 线下订单信息
     * @return 影响行数
     */
    int updateOfflineOrder(OfflineOrder offlineOrder);
    int updateOfflineOrderTemp(OfflineOrder offlineOrder);
    /**
     * 根据商户ID查询订单
     * 
     * @param merId 商户ID
     * @return 线下订单列表
     */
    List<OfflineOrder> selectOrdersByMerId(@Param("merId") Long merId);
    
    /**
     * 根据购买者openid查询订单
     * 
     * @param buyerOpenid 购买者openid
     * @return 线下订单列表
     */
    List<OfflineOrder> selectOrdersByBuyerOpenid(@Param("buyerOpenid") String buyerOpenid);
    List<OfflineOrder> selectOfflineUFOrders();
    int updateOrderState(@Param("orderCodeList") List<String> orderCodeList);
    OfflineOrder selectByOrderCode(OfflineOrder offlineOrder);
    int deleteSuccessOrdersTemp(@Param("orderCodeList")List<String> orderCodeList);
    int updateErrStateIntTemp(@Param("orderCodeList")List<String> orderCodeList);
    int updateErrStateInt(@Param("orderCodeList")List<String> orderCodeList);
    
    /**
     * 根据订单号查询单笔订单
     */
    OfflineOrder selectOfflineOrderByOrderCode(@Param("orderCode") String orderCode);
    int refundOrderState(@Param("orderCode") String orderCode);
    int deleteRefundTempOrder(@Param("orderCode") String orderCode);

}
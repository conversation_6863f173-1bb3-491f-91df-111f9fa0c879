package com.aggregate.payment.mapper;


import com.aggregate.payment.entity.MlCameraInfo;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MlCameraInfoMapper {
    int insertBatch(@Param("list") List<MlCameraInfo> list);
    int updateBatch(@Param("list") List<MlCameraInfo> list);
    int deleteBatchByIds(@Param("ids") List<Integer> ids);
    List<MlCameraInfo> selectBatchByIds(@Param("ids") List<Integer> ids);
    // 分页条件查询
    List<MlCameraInfo> selectByCondition(MlCameraInfo mlCameraInfo);
    int countByCondition(@Param("merId") Integer merchantId,
                        @Param("visible") Integer visible,
                        @Param("cameraName") String cameraName);

    MlCameraInfo selectById(@Param("id") Integer id);
    int upDateDelFlag(List<MlCameraInfo> mlCameraInfos);
}
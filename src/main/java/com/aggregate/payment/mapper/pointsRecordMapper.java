package com.aggregate.payment.mapper;

import com.aggregate.payment.entity.PointsRecord;
import com.aggregate.payment.entity.pointsSearch.PointsRecordQuery;
import com.aggregate.payment.entity.pointsSearch.PointsRecordVo;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;
import java.util.Map;

/**
 * 积分记录表 数据层
 * 
 * <AUTHOR>
 */
@Mapper
public interface pointsRecordMapper extends BaseMapper<PointsRecord> {

    /**
     * 批量插入积分记录
     * 
     * @param pointsRecords 积分记录列表
     * @return 影响行数
     */
    int insertList(@Param("list") List<PointsRecord> pointsRecords);

    /**
     * 查询积分记录列表
     * 
     * @param pointsRecord 查询条件
     * @return 积分记录列表
     */
    List<PointsRecord> selectList(PointsRecord pointsRecord);

    /**
     * 根据ID更新积分记录
     * 
     * @param pointsRecord 积分记录
     * @return 影响行数
     */
    int updateById(PointsRecord pointsRecord);

    /**
     * 批量更新积分记录
     *
     * @param pointsRecords 积分记录列表
     * @return 影响行数
     */
    int updateList(@Param("list") List<PointsRecord> pointsRecords);

    /**
     * 查询用户积分汇总统计
     *
     * @param pointsRecord 查询条件（主要使用userId和userType字段）
     * @return 积分统计信息 Map包含: totalEarned(总获得), totalExchanged(已兑换), totalAvailable(可用), totalFrozen(冻结)
     */
    Map<String, Object> selectUserpointsSummary(PointsRecord pointsRecord);

    /**
     * 查询用户积分明细列表
     *
     * @param pointsRecord 查询条件（可使用userId、type、status、userType、orderNo等字段）
     * @return 积分明细列表
     */
    List<PointsRecord> selectUserpointsDetails(PointsRecord pointsRecord);
    int selectUserpointsDetailsCount(PointsRecord pointsRecord);
    int ifExist(@Param("orderNo") String orderNo);
    List<PointsRecordVo> selectPointsRecordList(PointsRecordQuery query);
    int selectPointsRecordListCount(PointsRecordQuery query);
}

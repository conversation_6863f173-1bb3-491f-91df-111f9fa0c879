package com.aggregate.payment.mapper;

import com.aggregate.payment.entity.MerchantPoints;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MerchantpointsMapper {
    MerchantPoints selectUserpoints(Integer merId);
    int updateMlpoints(MerchantPoints merchantpoints);
    int batchUpdateMerchantPoints(@Param("list") List<MerchantPoints> merchantpoints);
    int batchUpdateMerchantPointsGetCount(@Param("list") List<MerchantPoints> merchantpoints);
}

package com.aggregate.payment.mapper;

import com.aggregate.payment.entity.MlOfflineSmsCode;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

@Mapper
public interface MlOfflineSmsCodeMapper {
    // 查最新一条验证码
    MlOfflineSmsCode selectLatestByPhone(@Param("phoneNumber") String phoneNumber);
    int insertSmsCode(MlOfflineSmsCode mlOfflineSmsCode);

    void markUsed(Long id);
}

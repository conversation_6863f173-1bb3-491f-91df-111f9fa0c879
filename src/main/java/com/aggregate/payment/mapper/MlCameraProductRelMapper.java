package com.aggregate.payment.mapper;

import com.aggregate.payment.entity.MlCameraProductRel;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MlCameraProductRelMapper {
    int insertBatch(@Param("list") List<MlCameraProductRel> list);
    int updateBatch(@Param("list") List<MlCameraProductRel> list);
    int deleteBatchByIds(@Param("ids") List<Integer> ids);
    List<MlCameraProductRel> selectByCondition(MlCameraProductRel condition);
    int unBind(@Param("list") List<MlCameraProductRel> list);

    /**
     * 查询某摄像机当前有效(未删除)的绑定记录
     */
    List<MlCameraProductRel> selectValidByCamera(@Param("cameraId") Long cameraId,
                                                @Param("merId") Long merId);

    /* ---- 单条CRUD ---- */
    int insertSingle(MlCameraProductRel rel);
    int updateSingle(MlCameraProductRel rel);
    int deleteSingle(MlCameraProductRel rel);

    MlCameraProductRel selectOne(MlCameraProductRel condition);
}
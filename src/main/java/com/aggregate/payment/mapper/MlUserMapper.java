package com.aggregate.payment.mapper;

import com.aggregate.payment.entity.Userpoints;
import org.apache.catalina.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@Mapper
public interface MlUserMapper {
    Userpoints selectMlUser(Integer uid);
    int updateMlUser(Userpoints userpoints);
    Userpoints selectMlUserE(Userpoints userpoints);
    List<Userpoints> selectUsers(@Param("accounts") List<String> account);
    int batchUpdateMlUsers(@Param("list") List<Userpoints> userpoints);
    int batchUpdateUserGetPoints(@Param("list") List<Userpoints> userpoints);
    Userpoints selectMlUserByAccount(@Param("account") String account);

    int insert(Userpoints us);
}

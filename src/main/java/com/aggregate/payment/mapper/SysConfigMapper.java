package com.aggregate.payment.mapper;

import com.aggregate.payment.entity.SysConfig;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 系统配置参数Mapper接口
 * 
 * <AUTHOR>
 */
@Mapper
public interface SysConfigMapper extends BaseMapper<SysConfig> {
    
    /**
     * 根据参数编码查询参数值
     */
    String selectValueByCode(@Param("code") String code);
    
    /**
     * 根据参数编码查询配置
     */
    SysConfig selectByCode(@Param("code") String code);
}
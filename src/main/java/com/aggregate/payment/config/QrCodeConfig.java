//package com.aggregate.payment.config;
//
//import lombok.extern.slf4j.Slf4j;
//import org.springframework.beans.factory.annotation.Value;
//import org.springframework.context.annotation.Configuration;
//
//import javax.annotation.PostConstruct;
//import java.io.File;
//
///**
// * 二维码配置类
// *
// * <AUTHOR>
// */
//@Slf4j
//@Configuration
//public class QrCodeConfig {
//
//    @Value("${aggregate.payment.qrcode-path}")
//    private String qrcodePath;
//
//    /**
//     * 初始化二维码存储目录
//     */
//    @PostConstruct
//    public void initQrCodeDirectory() {
//        try {
//            File qrcodeDir = new File(qrcodePath);
//            if (!qrcodeDir.exists()) {
//                boolean created = qrcodeDir.mkdirs();
//                if (created) {
//                    log.info("二维码存储目录创建成功: {}", qrcodeDir.getAbsolutePath());
//                } else {
//                    log.error("二维码存储目录创建失败: {}", qrcodeDir.getAbsolutePath());
//                }
//            } else {
//                log.info("二维码存储目录已存在: {}", qrcodeDir.getAbsolutePath());
//            }
//        } catch (Exception e) {
//            log.error("初始化二维码存储目录失败: {}", e.getMessage(), e);
//        }
//    }
//}

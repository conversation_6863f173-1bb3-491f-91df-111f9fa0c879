package com.aggregate.payment.service;

import com.aggregate.payment.entity.OfflineOrder;
import com.aggregate.payment.entity.Points2voucherDTO;
import com.aggregate.payment.entity.pointsSearch.PointsRecordQuery;
import com.aggregate.payment.entity.pointsSearch.PointsRecordVo;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface PointsRecordService {


    int InsertpointsRecord(List<Integer> orderIds);

    int updateUserpointsRecord(Points2voucherDTO voucherDTO);

    @Transactional
    int updateMerchantpointsRecord(Points2voucherDTO voucherDTO);

    int insertOfflineOrderScore(List<OfflineOrder> offlineOrders);



    List<PointsRecordVo> getPointsList(PointsRecordQuery query);

    int getPointsListCount(PointsRecordQuery query);
}

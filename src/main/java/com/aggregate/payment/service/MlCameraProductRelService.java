package com.aggregate.payment.service;

import com.aggregate.payment.entity.MlCameraProductRel;
import java.util.List;

public interface MlCameraProductRelService {
    int insertBatch(List<MlCameraProductRel> list);
    int updateBatch(List<MlCameraProductRel> list);
    int deleteBatchByIds(List<Integer> ids);
    List<MlCameraProductRel> selectByCondition(MlCameraProductRel condition);

    int unBind(List<MlCameraProductRel> list);


    /* ------------ 单条增删改查 -------------- */

    int insert(MlCameraProductRel rel);

    int update(MlCameraProductRel rel);

    /**
     * 根据 cameraId+productId+merId 逻辑删除
     */
    int delete(MlCameraProductRel rel);

    /**
     * 查询单条绑定记录
     */
    MlCameraProductRel selectOne(MlCameraProductRel condition);
}
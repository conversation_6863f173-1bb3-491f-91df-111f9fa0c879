package com.aggregate.payment.service;

import com.aggregate.payment.entity.AggregateQrCode;

import java.math.BigDecimal;
import java.util.List;

public interface IAggregateQrCodeService {
    List<AggregateQrCode> selectList(AggregateQrCode qrCode);

    AggregateQrCode selectByQrCodeKey(String qrCodeKey);

    /**
     * 根据商户ID查询二维码
     */
    AggregateQrCode selectByMerchantId(Long merchantId);

    /**
     * 创建新二维码（仅当商户没有二维码时）
     */
    String createQrCode(Long merchantId,
                        String qrCodeType);

    /**
     * 更新现有二维码
     */
    boolean updateQrCode(Long merchantId,
                        String qrCodeType);

    /**
     * 生成或更新二维码（智能判断）
     */
    String generateOrUpdateQrCode(Long merchantId,
                                  String qrCodeType);

    void updateScanCount(String qrCodeKey);

    String buildQrCodeUrl(String qrCodeKey);

    AggregateQrCode getByQrcodeKey(String qrCodeKey);
}

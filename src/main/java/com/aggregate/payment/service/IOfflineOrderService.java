package com.aggregate.payment.service;

import com.aggregate.payment.dto.OfflineOrderDto;
import com.aggregate.payment.entity.OfflineOrder;
import com.aggregate.payment.entity.payment.PayRespData;
import com.aggregate.payment.entity.payment.ResponseData;
import com.baomidou.mybatisplus.extension.service.IService;

import java.util.List;

/**
 * 线下订单服务接口
 * 
 * <AUTHOR>
 */
public interface IOfflineOrderService extends IService<OfflineOrder> {
    
    /**
     * 查询线下订单列表
     * 
     * @param offlineOrder 查询条件
     * @return 线下订单列表
     */
    List<OfflineOrder> selectOfflineOrderList(OfflineOrder offlineOrder);

    List<OfflineOrder> selectOfflineOrderListTemp(OfflineOrder offlineOrder);

    Integer selectOfflineOrderListTempCount(OfflineOrder offlineOrder);

    /**
     * 根据ID查询线下订单
     * 
     * @param id 订单ID
     * @return 线下订单信息
     */
    OfflineOrder selectOfflineOrderById(Long id);
    
    /**
     * 新增线下订单
     * 
     * @param offlineOrder 线下订单信息
     * @return 影响行数
     */
    int insertOfflineOrder(OfflineOrder offlineOrder);
    
    /**
     * 修改线下订单
     * 
     * @param offlineOrder 线下订单信息
     * @return 影响行数
     */
    int updateOfflineOrder(OfflineOrder offlineOrder);
    
    /**
     * 根据商户ID查询订单
     * 
     * @param merId 商户ID
     * @return 线下订单列表
     */
    List<OfflineOrder> selectOrdersByMerId(Long merId);
    
    /**
     * 根据购买者openid查询订单
     * 
     * @param buyerOpenid 购买者openid
     * @return 线下订单列表
     */
    List<OfflineOrder> selectOrdersByBuyerOpenid(String buyerOpenid);
    
    /**
     * 创建线下订单
     * 
     * @param offlineOrder 线下订单信息，需包含商户ID(merId)、购买者openid(buyerOpenid)、
     *                    商品名称(productName)、商品描述(productDescription)和支付金额(paymentAmount)
     * @return 创建成功的订单信息
     */
    PayRespData createOrder(OfflineOrderDto offlineOrder);
    
    /**
     * 更新订单支付状态
     * 
     * @param offlineOrder 线下订单信息，需包含订单ID(id)和支付状态(payState)
     * @return 是否更新成功
     */
    boolean updatePayState(OfflineOrder offlineOrder);
    
    /**
     * 更新订单状态
     * 
     * @param offlineOrder 线下订单信息，需包含订单ID(id)和订单状态(orderState)
     * @return 是否更新成功
     */
    boolean updateOrderState(OfflineOrder offlineOrder);
    int selectOfflineOrderCount(OfflineOrder offlineOrder);

    ResponseData RefundOfflineOrder(OfflineOrder offlineOrderDto);
}
package com.aggregate.payment.service;

import com.alipay.api.AlipayApiException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

public interface IPaymentAuthService {
    String detectScanSource(HttpServletRequest request);

    void handleScanRequest(HttpServletRequest request, HttpServletResponse response,
                           String qrCodeKey) throws IOException;

    void redirectToWechatAuth(HttpServletResponse response, String qrCodeKey) throws IOException;

    void redirectToAlipayAuth(HttpServletResponse response, String qrCodeKey) throws IOException;

    String getWechatOpenId(String code);

    String getAlipayUserId(String authCode) throws AlipayApiException;

    String getUserIdFromAuth(String authCode, String paymentType, String qrCodeKey);

    int initUser(String openId);
}

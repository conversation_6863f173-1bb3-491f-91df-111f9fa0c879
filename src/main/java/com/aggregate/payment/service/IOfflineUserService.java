package com.aggregate.payment.service;



import com.aggregate.payment.common.AjaxResult;
import com.aggregate.payment.entity.OfflineUser;
import com.aggregate.payment.entity.OfflineUserReInfo;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

public interface IOfflineUserService {
    int insertUserScore(OfflineUser userScore);
    List<OfflineUser> getUserScores(OfflineUser userScore);
    int updateUserScore(OfflineUser userScore);

    @Transactional
    AjaxResult bindPhoneNumber(OfflineUserReInfo offlineUser);

    AjaxResult getSms(String phone);

    AjaxResult verifyPhone(OfflineUserReInfo offlineUser);
}

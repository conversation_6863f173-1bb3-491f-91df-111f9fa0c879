package com.aggregate.payment.service;

import com.aggregate.payment.entity.MlCameraInfo;
import java.util.List;

public interface MlCameraInfoService {
    int insertBatch(List<MlCameraInfo> list);
    int updateBatch(List<MlCameraInfo> list);
    int deleteBatchByIds(List<Integer> ids);
    List<MlCameraInfo> selectBatchByIds(List<Integer> ids);
    List<MlCameraInfo> selectByCondition(MlCameraInfo mlCameraInfo);
    int countByCondition(Integer merchantId, Integer visible, String cameraName);

    List<MlCameraInfo> importCameras(Integer merId);

    Object getPlayUrl(Integer id);
}
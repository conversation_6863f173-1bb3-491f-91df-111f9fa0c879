package com.aggregate.payment.service.impl;

import com.aggregate.payment.entity.MerchantPoints;
import com.aggregate.payment.entity.MlCameraInfo;
import com.aggregate.payment.entity.MlCameraProductRel;
import com.aggregate.payment.entity.payment.ResponseData;
import com.aggregate.payment.mapper.MerchantpointsMapper;
import com.aggregate.payment.mapper.MlCameraInfoMapper;
import com.aggregate.payment.service.MlCameraInfoService;
import com.aggregate.payment.service.MlCameraProductRelService;
import com.aggregate.payment.util.HttpClientUtil;
import com.alibaba.fastjson2.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class MlCameraInfoServiceImpl implements MlCameraInfoService {
    private static final Logger logger = LoggerFactory.getLogger(MlCameraInfoServiceImpl.class);
    @Autowired
    private MlCameraProductRelService mlCameraProductRelService;
    @Autowired
    private MlCameraInfoMapper mlCameraInfoMapper;
    @Autowired
    private MerchantpointsMapper merchantpointsMapper;

    @Override
    public int insertBatch(List<MlCameraInfo> list) {
        return mlCameraInfoMapper.insertBatch(list);
    }

    @Override
    public int updateBatch(List<MlCameraInfo> list) {
        return mlCameraInfoMapper.updateBatch(list);
    }

    @Override
    public int deleteBatchByIds(List<Integer> ids) {
        return mlCameraInfoMapper.deleteBatchByIds(ids);
    }

    @Override
    public List<MlCameraInfo> selectBatchByIds(List<Integer> ids) {
        return mlCameraInfoMapper.selectBatchByIds(ids);
    }

    @Override
    public List<MlCameraInfo> selectByCondition(MlCameraInfo mlCameraInfo) {
        return mlCameraInfoMapper.selectByCondition(mlCameraInfo);
    }

    @Override
    public int countByCondition(Integer merchantId, Integer visible, String cameraName) {
        return mlCameraInfoMapper.countByCondition(merchantId, visible, cameraName);
    }

    @Override
    @Transactional
    public List<MlCameraInfo> importCameras(Integer merId) {
        MerchantPoints merchantPoints = merchantpointsMapper.selectUserpoints(merId);
        if (merchantPoints == null) {
            throw new RuntimeException("商户不存在");
        }
        String businessLicense = merchantPoints.getBusinessLicense();
        Map<String, String> map = new HashMap<>();
        map.put("userId", businessLicense);
        String s = HttpClientUtil.post("https://pay.fengzhidi.com/tracesource/v1/camera/getCameraList")
                .formBody(map)
                .connectTimeout(30000)
                .executeAsString();
        logger.error("远程回复信息{}", s);
        ResponseData resp = JSON.parseObject(s, ResponseData.class);
        logger.error("解析远程回复信息{}", resp);
        if (!"00000".equals(resp.getErrorCode())) {
            throw new RuntimeException("导入异常: " + resp.getErrorMsg());
        }

        // 1. 拉取远程摄像头列表
        List<MlCameraInfo> remoteList = JSON.parseArray(JSON.toJSONString(resp.getRspData()), MlCameraInfo.class);
        remoteList.forEach(camera -> {
            camera.setVisible(1);
            camera.setMerId(merchantPoints.getMerId());
        });

        // 2. 本地已有摄像头
        MlCameraInfo query = new MlCameraInfo();
        query.setMerId(merId);
        List<MlCameraInfo> localList = selectByCondition(query);

        // 3. 建立映射
        Map<String, MlCameraInfo> remoteMap = remoteList.stream()
                .collect(Collectors.toMap(MlCameraInfo::getCameraIndexCode, Function.identity()));
        Map<String, MlCameraInfo> localMap = localList.stream()
                .collect(Collectors.toMap(MlCameraInfo::getCameraIndexCode, Function.identity()));

        // 4. 分类处理
        List<MlCameraInfo> toInsert = new ArrayList<>();
        List<MlCameraInfo> toUpdate = new ArrayList<>();
        List<MlCameraInfo> toDelete = new ArrayList<>();

        // 找出新增和更新
        for (Map.Entry<String, MlCameraInfo> entry : remoteMap.entrySet()) {
            String indexCode = entry.getKey();
            MlCameraInfo remoteCamera = entry.getValue();
            if (localMap.containsKey(indexCode)) {
                MlCameraInfo localCamera = localMap.get(indexCode);
                // 设置ID，便于update
                remoteCamera.setId(localCamera.getId());
                toUpdate.add(remoteCamera);
            } else {
                toInsert.add(remoteCamera);
            }
        }

        // 找出需要删除的（本地有远程没有）
        for (Map.Entry<String, MlCameraInfo> entry : localMap.entrySet()) {
            if (!remoteMap.containsKey(entry.getKey())) {
                toDelete.add(entry.getValue());
            }
        }

        // 5. 执行操作
        if (!toInsert.isEmpty()) {
            insertBatch(toInsert);
        }

        if (!toUpdate.isEmpty()) {
            updateBatch(toUpdate);
        }

        if (!toDelete.isEmpty()) {
            mlCameraInfoMapper.upDateDelFlag(toDelete);
            mlCameraProductRelService.unBind(toDelete.stream().map(camera -> {
                MlCameraProductRel rel = new MlCameraProductRel();
                rel.setCameraId(camera.getId());
                rel.setMerId(merId);
                return rel;
            }).collect(Collectors.toList()));
        }

        return remoteList;
    }

    @Override
    public Object getPlayUrl(Integer id) {
        MlCameraInfo mlCameraInfo = mlCameraInfoMapper.selectById(id);
        String cameraIndexCode = mlCameraInfo.getCameraIndexCode();
        Map<String, String> map = new HashMap<>();
        map.put("protocol", "wss");
        map.put("cameraIndexCode", cameraIndexCode);
        map.put("streamType", "0");
        String s = HttpClientUtil.post("https://pay.fengzhidi.com/tracesource/v1/camera/getCameraPreviewURL")
                .formBody(map)
                .connectTimeout(30000)
                .executeAsString();
        ResponseData responseData = JSON.parseObject(s, ResponseData.class);
        logger.info("请求回复{}", JSON.toJSONString(responseData));
        if (responseData.getErrorCode().equals("00000")) {
            logger.info("请求回复{}", JSON.toJSONString(responseData.getRspData()));
            return responseData.getRspData();
        } else {
            return ("获取播放地址失败: " + responseData.getErrorMsg());
        }
    }
} 
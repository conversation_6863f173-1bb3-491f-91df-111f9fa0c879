package com.aggregate.payment.service.impl;

import cn.hutool.core.date.DateTime;
import com.aggregate.payment.entity.AlipayConfig;

import com.aggregate.payment.entity.OfflineUser;
import com.aggregate.payment.service.IPaymentAuthService;
import com.aggregate.payment.service.IOfflineUserService;
import com.alibaba.fastjson2.JSONObject;
import com.alipay.api.AlipayApiException;
import com.alipay.api.AlipayClient;
import com.alipay.api.DefaultAlipayClient;
import com.alipay.api.request.AlipaySystemOauthTokenRequest;
import com.alipay.api.response.AlipaySystemOauthTokenResponse;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.time.LocalDateTime;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.time.LocalDateTime;
import java.util.Date;
import java.util.List;

/**
 * 支付授权服务类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class PaymentAuthServiceImpl implements IPaymentAuthService {

    @Autowired
    private RestTemplate restTemplate;
    @Autowired
    private IOfflineUserService userScoreService;

    @Value("${aggregate.payment.domain}")
    private String domain;

    @Value("${aggregate.payment.wechat.app-id}")
    private String wechatAppId;

    @Value("${aggregate.payment.wechat.app-secret}")
    private String wechatAppSecret;

    @Value("${aggregate.payment.alipay.app-id}")
    private String alipayAppId;

    @Value("${aggregate.payment.alipay.private-key}")
    private String alipayPrivateKey;

    @Value("${aggregate.payment.alipay.public-key}")
    private String alipayPublicKey;

    @Value("${aggregate.payment.alipay.gateway-url}")
    private String alipayGatewayUrl;


    /**
     * 检测扫码来源
     */
    @Override
    public String detectScanSource(HttpServletRequest request) {
        String userAgent = request.getHeader("User-Agent");
        if (userAgent != null) {
            if (userAgent.contains("MicroMessenger")) {
                return "WECHAT";
            } else if (userAgent.contains("AlipayClient")) {
                return "ALIPAY";
            }
        }
        return "BROWSER";
    }

    /**
     * 处理扫码请求
     */
    @Override
    public void handleScanRequest(HttpServletRequest request, HttpServletResponse response,
                                  String qrCodeKey) throws IOException {
        String scanSource = detectScanSource(request);
        
        switch (scanSource) {
            case "WECHAT":
                redirectToWechatAuth(response, qrCodeKey);
                break;
            case "ALIPAY":
                redirectToAlipayAuth(response, qrCodeKey);
                break;
            default:
                break;
        }
    }

    /**
     * 重定向到微信授权
     */
    @Override
    public void redirectToWechatAuth(HttpServletResponse response, String qrCodeKey) throws IOException {
        String callbackUrl = domain + "/aggregate-pay/auth/wechat/callback";
        String authUrl = "https://open.weixin.qq.com/connect/oauth2/authorize?" +
                "appid=" + wechatAppId +
                "&redirect_uri=" + URLEncoder.encode(callbackUrl, "UTF-8") +
                "&response_type=code" +
                "&scope=snsapi_base" +
                "&state=" + qrCodeKey +
                "#wechat_redirect";
        
        log.info("重定向到微信授权: {}", authUrl);
        response.sendRedirect(authUrl);
    }

    /**
     * 重定向到支付宝授权
     */
    @Override
    public void redirectToAlipayAuth(HttpServletResponse response, String qrCodeKey) throws IOException {
        String callbackUrl = domain + "/aggregate-pay/auth/alipay/callback";
        String authUrl = "https://openauth.alipay.com/oauth2/publicAppAuthorize.htm?" +
                "app_id=" + alipayAppId +
                "&scope=auth_base" +
                "&redirect_uri=" + URLEncoder.encode(callbackUrl, "UTF-8") +
                "&state=" + qrCodeKey;
        
        log.info("重定向到支付宝授权: {}", authUrl);
        response.sendRedirect(authUrl);
    }

    /**
     * 获取微信用户openid
     */
    @Override
    public String getWechatOpenId(String code) {
        try {
            String url = "https://api.weixin.qq.com/sns/oauth2/access_token?" +
                    "appid=" + wechatAppId +
                    "&secret=" + wechatAppSecret +
                    "&code=" + code +
                    "&grant_type=authorization_code";
            
            String response = restTemplate.getForObject(url, String.class);
            JSONObject jsonObject = JSONObject.parseObject(response);
            
            if (jsonObject.containsKey("errcode")) {
                log.error("微信授权失败: {}", jsonObject.getString("errmsg"));
                return null;
            }
            
            return jsonObject.getString("openid");
        } catch (Exception e) {
            log.error("获取微信openid失败", e);
            return null;
        }
    }

    /**
     * 获取支付宝用户ID
     */
    @Override
    public String getAlipayUserId(String authCode) throws AlipayApiException {
        // 获取支付宝配置
        AlipayConfig config = getAlipayConfig();

        // 初始化SDK - 使用正确的构造函数参数
        AlipayClient alipayClient = new DefaultAlipayClient(
            config.getServerUrl(),
            config.getAppId(),
            config.getPrivateKey(),
            config.getFormat(),
            config.getCharset(),
            config.getAlipayPublicKey(),
            config.getSignType()
        );

        // 构造请求参数以调用接口
        AlipaySystemOauthTokenRequest request = new AlipaySystemOauthTokenRequest();

        // 设置授权码
        request.setCode(authCode);

        // 设置授权方式
        request.setGrantType("authorization_code");

        AlipaySystemOauthTokenResponse response = alipayClient.execute(request);
        System.out.println(response.getBody());

        if (response.isSuccess()) {
            System.out.println("调用成功");
            return response.getUserId();
        } else {
            System.out.println("调用失败");
            // sdk版本是"4.38.0.ALL"及以上,可以参考下面的示例获取诊断链接
            // String diagnosisUrl = DiagnosisUtils.getDiagnosisUrl(response);
            // System.out.println(diagnosisUrl);
            return null;
        }
    }

    /**
     * 获取支付宝配置
     */
    private AlipayConfig getAlipayConfig() {
        AlipayConfig alipayConfig = new AlipayConfig();
        alipayConfig.setServerUrl(alipayGatewayUrl);
        alipayConfig.setAppId(alipayAppId);
        alipayConfig.setPrivateKey(alipayPrivateKey);
        alipayConfig.setFormat("json");
        alipayConfig.setAlipayPublicKey(alipayPublicKey);
        alipayConfig.setCharset("UTF-8");
        alipayConfig.setSignType("RSA2");
        return alipayConfig;
    }

    /**
     * 统一获取用户ID方法
     */
    @Override
    public String getUserIdFromAuth(String authCode, String paymentType, String qrCodeKey) {
        String userId = null;
        
        try {
            if ("WECHAT".equals(paymentType)) {
                userId = getWechatOpenId(authCode);
            } else if ("ALIPAY".equals(paymentType)) {
                userId = getAlipayUserId(authCode);
            }else{
                throw new RuntimeException("请使用支付宝或微信扫码");
            }

            
            if (userId != null) {
                log.info("获取用户ID成功: paymentType={}, userId={}, qrCodeKey={}", 
                        paymentType, userId, qrCodeKey);

            }
            
        } catch (Exception e) {
            log.error("获取用户授权失败: {}", e.getMessage());
        }
        
        return userId;
    }
    /**
     * 初始化用户信息
     *
     * @param openId 用户openId
     * @return 初始化结果：0=成功(已绑定手机号), 1=需要绑定手机号, -1=失败
     */
    @Override
    public int initUser(String openId) {
        try {
            OfflineUser userScore = new OfflineUser();
            userScore.setOpenId(openId);
            List<OfflineUser> userScores = userScoreService.getUserScores(userScore);

            if (!userScores.isEmpty()) {
                // 用户已存在，检查是否绑定手机号
                userScore = userScores.get(0);
                String phone = userScore.getPhone();

                if (phone != null && !phone.trim().isEmpty()) {
                    // 用户已绑定手机号，可以正常使用
                    log.info("用户验证成功: openId={}, phone={}", openId, phone);
                    return 0;
                } else {
                    // 用户存在但未绑定手机号
                    log.info("用户未绑定手机号: openId={}", openId);
                    return 1;
                }
            } else {
                LocalDateTime now = LocalDateTime.now();
                // 用户不存在，创建新用户
                log.info("用户不存在，创建新用户: openId={}", openId);
                userScore.setOpenId(openId);
                userScore.setCreateTime(now);
                userScore.setUpdateTime(now);

                int insertResult = userScoreService.insertUserScore(userScore);
                if (insertResult > 0) {
                    // 新用户创建成功，需要绑定手机号
                    log.info("新用户创建成功: openId={}", openId);
                    return 1;
                } else {
                    log.error("创建用户失败: openId={}", openId);
                    return -1;
                }
            }

        } catch (Exception e) {
            log.error("用户初始化异常: openId={}, error={}", openId, e.getMessage(), e);
            return -1;
        }
    }

}

package com.aggregate.payment.service.impl;

import com.aggregate.payment.entity.MlCameraProductRel;
import com.aggregate.payment.mapper.MlCameraProductRelMapper;
import com.aggregate.payment.service.MlCameraProductRelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.*;

@Service
public class MlCameraProductRelServiceImpl implements MlCameraProductRelService {
    @Autowired
    private MlCameraProductRelMapper mlCameraProductRelMapper;

    @Override
    public int insertBatch(List<MlCameraProductRel> list) {

        return mlCameraProductRelMapper.insertBatch(list);
    }

    @Override
    public int updateBatch(List<MlCameraProductRel> list) {
        return mlCameraProductRelMapper.updateBatch(list);
    }

    @Override
    public int deleteBatchByIds(List<Integer> ids) {
        return mlCameraProductRelMapper.deleteBatchByIds(ids);
    }

    @Override
    public List<MlCameraProductRel> selectByCondition(MlCameraProductRel condition) {
        return mlCameraProductRelMapper.selectByCondition(condition);
    }
    @Override
    public int unBind(List<MlCameraProductRel> list) {
        return mlCameraProductRelMapper.unBind(list);
    }




    @Override
    public int insert(MlCameraProductRel rel) {
        if (rel == null) {
            return 0;
        }
        if (rel.getDelflag() == null) {
            rel.setDelflag(0);
        }
        return mlCameraProductRelMapper.insertSingle(rel);
    }

    @Override
    public int update(MlCameraProductRel rel) {
        return mlCameraProductRelMapper.updateSingle(rel);
    }

    @Override
    public int delete(MlCameraProductRel rel) {
        return mlCameraProductRelMapper.deleteSingle(rel);
    }

    @Override
    public MlCameraProductRel selectOne(MlCameraProductRel condition) {
        return mlCameraProductRelMapper.selectOne(condition);
    }
}
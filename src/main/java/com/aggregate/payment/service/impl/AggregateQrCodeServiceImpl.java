package com.aggregate.payment.service.impl;

import com.aggregate.payment.entity.AggregateQrCode;
import com.aggregate.payment.entity.MerchantPoints;
import com.aggregate.payment.mapper.MerchantpointsMapper;
import com.aggregate.payment.service.IAggregateQrCodeService;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.aggregate.payment.mapper.AggregateQrCodeMapper;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;
import java.util.UUID;

/**
 * 聚合二维码服务类
 * 
 * <AUTHOR>
 */
@Service
public class AggregateQrCodeServiceImpl  extends ServiceImpl<AggregateQrCodeMapper, AggregateQrCode> implements IAggregateQrCodeService {
    @Value("${aggregate.payment.QRdomain}")
    private String QRdomain;
    @Autowired
    private MerchantpointsMapper merchantpointsMapper;
    @Autowired
    private MerchantpointsMapper mapper;
    /**
     * 查询二维码列表
     */
    @Override
    public List<AggregateQrCode> selectList(AggregateQrCode qrCode) {
        LambdaQueryWrapper<AggregateQrCode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(qrCode.getMerchantId() != null, AggregateQrCode::getMerchantId, qrCode.getMerchantId())
               .eq(qrCode.getQrCodeType() != null, AggregateQrCode::getQrCodeType, qrCode.getQrCodeType())
               .eq(qrCode.getStatus() != null, AggregateQrCode::getStatus, qrCode.getStatus())
               .eq(AggregateQrCode::getDelFlag, "0")
               .orderByDesc(AggregateQrCode::getCreateTime);
        return list(wrapper);
    }

    /**
     * 根据二维码标识查询
     */
    @Override
    public AggregateQrCode selectByQrCodeKey(String qrCodeKey) {
        LambdaQueryWrapper<AggregateQrCode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AggregateQrCode::getQrCodeKey, qrCodeKey)
               .eq(AggregateQrCode::getDelFlag, "0");
        return getOne(wrapper);
    }

    /**
     * 根据商户ID查询二维码
     */
    @Override
    public AggregateQrCode selectByMerchantId(Long merchantId) {
        LambdaQueryWrapper<AggregateQrCode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AggregateQrCode::getMerchantId, merchantId)
               .eq(AggregateQrCode::getDelFlag, "0")
               .orderByDesc(AggregateQrCode::getCreateTime)
               .last("LIMIT 1");
        return getOne(wrapper);
    }

    /**
     * 创建新二维码（仅当商户没有二维码时）
     */
    @Override
    public String createQrCode(Long merchantId,
                               String qrCodeType) {
        // 检查商户是否已有二维码
        AggregateQrCode existingQrCode = selectByMerchantId(merchantId);
        if (existingQrCode != null) {
            throw new RuntimeException("商户已存在二维码，请使用更新接口");
        }

        return doCreateQrCode(merchantId,  qrCodeType);
    }

    /**
     * 更新现有二维码
     */
    @Override
    public boolean updateQrCode(Long merchantId,
                               String qrCodeType) {
        MerchantPoints merchantpoints = merchantpointsMapper.selectUserpoints(Math.toIntExact(merchantId));

        // 检查商户是否存在
        if (merchantpoints == null) {
            throw new RuntimeException("商户不存在，merchantId: " + merchantId);
        }

        BigDecimal commissionRate = merchantpoints.getCommissionRate();
        String merchantName= merchantpoints.getMerName();
        // 查找现有二维码
        AggregateQrCode existingQrCode = selectByMerchantId(merchantId);
        if (existingQrCode == null) {
            throw new RuntimeException("商户二维码不存在，请使用创建接口");
        }

        // 更新二维码信息
        existingQrCode.setMerchantCode(merchantName);
        existingQrCode.setServiceFeeRate(commissionRate);

        existingQrCode.setQrCodeType(qrCodeType);
        existingQrCode.setUpdateTime(LocalDateTime.now());

        return updateById(existingQrCode);
    }

    /**
     * 生成或更新二维码（智能判断）
     */
    @Override
    public String generateOrUpdateQrCode(Long merchantId,
                                        String qrCodeType) {
        MerchantPoints merchantPoints = mapper.selectUserpoints(Math.toIntExact(merchantId));

        // 先检查商户是否存在
        if (merchantPoints == null) {
            throw new RuntimeException("商户不存在，merchantId: " + merchantId);
        }

        if (StringUtils.isNotBlank(merchantPoints.getBankUserCode())) {
            AggregateQrCode existingQrCode = selectByMerchantId(merchantId);
            if (existingQrCode != null) {
                // 更新现有二维码
                updateQrCode(merchantId,   qrCodeType);
                return existingQrCode.getQrCodeKey();
            } else {
                // 创建新二维码
                return doCreateQrCode(merchantId,   qrCodeType);
            }
        }else{
            throw new RuntimeException("请先完成开户");
        }
    }

    /**
     * 实际创建二维码的方法
     */
    private String doCreateQrCode(Long merchantId,
                                  String qrCodeType) {
        MerchantPoints merchantpoints = merchantpointsMapper.selectUserpoints(Math.toIntExact(merchantId));

        // 检查商户是否存在
        if (merchantpoints == null) {
            throw new RuntimeException("商户不存在，merchantId: " + merchantId);
        }

        BigDecimal commissionRate = merchantpoints.getCommissionRate();
        // 生成唯一标识
        String qrCodeKey = merchantId + "_" + System.currentTimeMillis() + "_" +
                          UUID.randomUUID().toString().replace("-", "").substring(0, 8);

        // 构建二维码URL
        String qrCodeUrl = buildQrCodeUrl(qrCodeKey);

        // 保存二维码信息
        AggregateQrCode qrCode = new AggregateQrCode();
        qrCode.setQrCodeKey(qrCodeKey);
        qrCode.setMerchantId(merchantId);
        qrCode.setQrCodeType(qrCodeType);
        qrCode.setMerchantCode(merchantpoints.getMerName());

        qrCode.setServiceFeeRate(commissionRate);
        qrCode.setQrCodeUrl(qrCodeUrl);
        qrCode.setStatus("0");
        qrCode.setDelFlag("0");
        qrCode.setCreateTime(LocalDateTime.now());
        qrCode.setScanCount(0);

        save(qrCode);

        return qrCodeKey;
    }

    /**
     * 更新扫码统计
     */
    @Override
    public void updateScanCount(String qrCodeKey) {
        AggregateQrCode qrCode = selectByQrCodeKey(qrCodeKey);
        if (qrCode != null) {
            qrCode.setScanCount(qrCode.getScanCount() + 1);
            qrCode.setLastScanTime(LocalDateTime.now());
            updateById(qrCode);
        }
    }

    /**
     * 构建二维码URL
     */
    @Override
    public String buildQrCodeUrl(String qrCodeKey) {
        return QRdomain+"/aggregate-pay/scan?code=" + qrCodeKey;
    }
    @Override
    public AggregateQrCode getByQrcodeKey(String qrCodeKey){
        LambdaQueryWrapper<AggregateQrCode> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(AggregateQrCode::getQrCodeKey, qrCodeKey)
               .eq(AggregateQrCode::getDelFlag, "0");
        return getOne(wrapper);
    }
}

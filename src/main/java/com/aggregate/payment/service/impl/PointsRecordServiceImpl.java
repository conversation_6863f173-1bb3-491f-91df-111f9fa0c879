package com.aggregate.payment.service.impl;

import com.aggregate.payment.entity.*;
import com.aggregate.payment.entity.pointsSearch.PointsRecordQuery;
import com.aggregate.payment.entity.pointsSearch.PointsRecordVo;
import com.aggregate.payment.mapper.*;

import com.aggregate.payment.service.PointsRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;

@Service
public class PointsRecordServiceImpl implements PointsRecordService {
    @Autowired
    private MlStoreOrderMapper mlStoreOrderMapper;
    @Autowired
    private MlUserMapper mlUserMapper;
    @Autowired
    private MerchantpointsMapper merchantpointsMapper;
    @Autowired
    private pointsRecordMapper pointsRecordMapper;
    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Value("${aggregate.payment.totalPeriods}")
    private String dictKey;
    @Autowired
    private PendingPointsMapper pendingPointsMapper;

    // 积分计算倍率配置key
    private static final String RATE_CONFIG = "points_calculation_rate";
    @Value("${aggregate.payment.points_coefficient}")
    private String rateConfig;
    private Integer rate;


    @Transactional
    @Override
    public int InsertpointsRecord(List<Integer> orderIds) {
        String totalPeriods = sysConfigMapper.selectValueByCode(dictKey);
        rate = Integer.parseInt(sysConfigMapper.selectValueByCode(rateConfig));
        List<OrderBaseInfo> orderBaseInfos = mlStoreOrderMapper.selectOrderBaseInfo(orderIds);
        List<PointsRecord> pointsRecords = new ArrayList<>();
        LocalDateTime now = LocalDateTime.now();
        List<Userpoints> ups=new ArrayList<>();
        List<MerchantPoints> mps=new ArrayList<>();
        List<Userpoints> uGetps=new ArrayList<>();
        List<MerchantPoints> mGetps=new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        int dateChar = Integer.parseInt(now.format(formatter));
        List<PendingPointsDto> pendingPointsDtoList =new ArrayList<>();
        for (OrderBaseInfo orderBaseInfo : orderBaseInfos) {
            BigDecimal commissionRate = orderBaseInfo.getCommissionRate().divide(BigDecimal.valueOf(100),10, RoundingMode.DOWN);
            int i = ifExist(orderBaseInfo.getOrderSn());
            if (i > 0) {
                continue;
            }
            BigDecimal payPrice = orderBaseInfo.getPayPrice();
            if(payPrice==null||payPrice.compareTo(BigDecimal.ZERO)==0){
               continue;
            }
            BigDecimal pointsDecimal = commissionRate.multiply(payPrice).multiply(BigDecimal.valueOf(rate)).setScale(2, RoundingMode.UP);
            float points = pointsDecimal.floatValue();
            if (points < 0.01f) {
                points = 0.01f;
            }
            Integer uid = orderBaseInfo.getUid();
            Integer merId = orderBaseInfo.getMerId();
            Userpoints userpoints = mlUserMapper.selectMlUser(uid);
            //商家积分余额更新
            MerchantPoints merchantpoints = new MerchantPoints();
            merchantpoints.setMerId(merId);
            merchantpoints.setPoints(points);
            merchantpoints.setPointsGetCount(BigDecimal.valueOf(points));
            mps.add(merchantpoints);
            //商家累计积分更新
            mGetps.add(merchantpoints);
            //积分记录
            PointsRecord pointsMerchantRecord = new PointsRecord();
            pointsMerchantRecord.setOrderNo(orderBaseInfo.getOrderSn());
            pointsMerchantRecord.setUserId(merId.toString());
            pointsMerchantRecord.setPoints(points);
            pointsMerchantRecord.setDescription("用户下单获得积分");
            pointsMerchantRecord.setType(1);
            pointsMerchantRecord.setUserType(1);
            pointsMerchantRecord.setStatus(1);
            pointsMerchantRecord.setTotalPeriods(totalPeriods);
            pointsMerchantRecord.setCreateTime(now);
            pointsMerchantRecord.setUpdateTime(now);
            pointsMerchantRecord.setDateChar(dateChar);
            pointsMerchantRecord.setOrderType(1);
            pointsMerchantRecord.setInvitorTag(0);
            pointsRecords.add(pointsMerchantRecord);
            //商户待统计积分信息
            PendingPointsDto merDto = new PendingPointsDto();
            merDto.setUserType(1);
            merDto.setUserId(Long.valueOf(merId));
            merDto.setPoints(BigDecimal.valueOf(points));
            merDto.setBatchNo(dateChar);
            merDto.setPeriodNum(Integer.parseInt(totalPeriods));
            merDto.setCreateTime(now);
            pendingPointsDtoList.add(merDto);
            if (userpoints != null) {
                //积分余额以及累计积分更新
                Integer serviceProviderId = userpoints.getServiceProviderId();
                userpoints.setUid(uid);
                userpoints.setPoints(points);
                userpoints.setPointsGetCount(BigDecimal.valueOf(points));
                ups.add(userpoints);
                uGetps.add(userpoints);
                //积分记录更新
                PointsRecord pointsRecord = new PointsRecord();
                pointsRecord.setOrderNo(orderBaseInfo.getOrderSn());
                pointsRecord.setUserId(uid.toString());
                pointsRecord.setPoints(points);
                pointsRecord.setDescription("下单赠送积分");
                pointsRecord.setType(1);
                pointsRecord.setStatus(1);
                pointsRecord.setUserType(0);
                pointsRecord.setTotalPeriods(totalPeriods);
                pointsRecord.setCreateTime(now);
                pointsRecord.setUpdateTime(now);
                pointsRecords.add(pointsRecord);
                pointsRecord.setOrderType(1);
                pointsRecord.setInvitorTag(0);
                pointsRecord.setDateChar(dateChar);
                //用户待统计积分信息
                PendingPointsDto userDto = new PendingPointsDto();
                userDto.setUserType(0);
                userDto.setUserId(Long.valueOf(uid));
                userDto.setPoints(BigDecimal.valueOf(points));
                userDto.setBatchNo(dateChar);
                userDto.setPeriodNum(Integer.parseInt(totalPeriods));
                userDto.setCreateTime(now);
                pendingPointsDtoList.add(userDto);
                if (serviceProviderId != null && serviceProviderId!= 0) {
                    PointsRecord pointsSpreadRecord = new PointsRecord();
                    BigDecimal sPointsDecimal = new BigDecimal(Float.toString(points)).multiply(new BigDecimal("0.05")).setScale(2, RoundingMode.UP);
                    float SPoints = sPointsDecimal.floatValue();
                     // 邀请人积分也设置最小值
                    if (SPoints < 0.01f) {
                        SPoints = 0.01f;
                    }
                    if (userpoints.getServiceProviderType() == 0) {
                        //待统计积分
                        PendingPointsDto spreadUserDto = new PendingPointsDto();
                        spreadUserDto.setUserType(0);
                        spreadUserDto.setUserId(Long.valueOf(serviceProviderId));
                        spreadUserDto.setPoints(BigDecimal.valueOf(SPoints));
                        spreadUserDto.setBatchNo(dateChar);
                        spreadUserDto.setPeriodNum(Integer.parseInt(totalPeriods));
                        spreadUserDto.setCreateTime(now);
                        pendingPointsDtoList.add(spreadUserDto);
                        //用户积分记录更新
                        Userpoints spreadUser = new Userpoints();
                        spreadUser.setUid(serviceProviderId);
                        spreadUser.setPoints(SPoints);
                        spreadUser.setPointsGetCount(BigDecimal.valueOf(SPoints));
                        //积分余额以及累计积分更新
                        ups.add(spreadUser);
                        uGetps.add(spreadUser);
                        pointsSpreadRecord.setUserType(0);

                    } else if (userpoints.getServiceProviderType() == 1) {
                        //待统计积分
                        PendingPointsDto spreadMerDto = new PendingPointsDto();
                        spreadMerDto.setUserType(1);
                        spreadMerDto.setUserId(Long.valueOf(serviceProviderId));
                        spreadMerDto.setPoints(BigDecimal.valueOf(SPoints));
                        spreadMerDto.setBatchNo(dateChar);
                        spreadMerDto.setPeriodNum(Integer.parseInt(totalPeriods));
                        spreadMerDto.setCreateTime(now);
                        pendingPointsDtoList.add(spreadMerDto);
                        //积分更新
                        MerchantPoints spreadMerchant = new MerchantPoints();
                        spreadMerchant.setMerId(serviceProviderId);
                        spreadMerchant.setPoints(SPoints);
                        spreadMerchant.setPointsGetCount(BigDecimal.valueOf(SPoints));
                        //积分余额以及累计积分更新
                        mps.add(spreadMerchant);
                        mGetps.add(spreadMerchant);
                        pointsSpreadRecord.setUserType(1);
                    }


                    pointsSpreadRecord.setOrderNo(orderBaseInfo.getOrderSn());
                    pointsSpreadRecord.setUserId(serviceProviderId.toString());
                    pointsSpreadRecord.setPoints(SPoints);
                    pointsSpreadRecord.setDescription("邀请得积分");
                    pointsSpreadRecord.setType(1);
                    pointsSpreadRecord.setStatus(1);
                    pointsSpreadRecord.setDateChar(dateChar);
                    pointsSpreadRecord.setTotalPeriods(totalPeriods);
                    pointsSpreadRecord.setCreateTime(now);
                    pointsSpreadRecord.setUpdateTime(now);
                    pointsSpreadRecord.setOrderType(1);
                    pointsSpreadRecord.setInvitorTag(1);
                    pointsRecords.add(pointsSpreadRecord);

                }
            }


        }
        if (!ups.isEmpty()) {
            mlUserMapper.batchUpdateMlUsers(ups);
            mlUserMapper.batchUpdateUserGetPoints(uGetps);
        }
        if (!mps.isEmpty()) {
            merchantpointsMapper.batchUpdateMerchantPoints(mps);
            merchantpointsMapper.batchUpdateMerchantPointsGetCount(mGetps);
        }
        if (!pendingPointsDtoList.isEmpty()) {
            pendingPointsMapper.BatchInsertPendingPoints(pendingPointsDtoList);
        }
        if (!pointsRecords.isEmpty()) {
            return pointsRecordMapper.insertList(pointsRecords);
        }
        return 0;
    }


    @Transactional
    @Override
    public int updateUserpointsRecord(Points2voucherDTO voucherDTO) {
        Integer uid = voucherDTO.getUid();
        Float points = voucherDTO.getPoints();
        Userpoints userpoints = mlUserMapper.selectMlUser(uid);
        userpoints.setUid(uid);
        userpoints.setPoints(-points);
        mlUserMapper.updateMlUser(userpoints);
        PointsRecord pointsRecord = new PointsRecord();
        pointsRecord.setOrderNo("兑换" + uid);
        pointsRecord.setUserId(uid.toString());
        pointsRecord.setPoints(-points);
        pointsRecord.setDescription("兑换代金券" + points + "积分");
        pointsRecord.setType(0);
        pointsRecord.setStatus(1);
        pointsRecord.setUserType(2);
        pointsRecord.setCreateTime(LocalDateTime.now());
        pointsRecord.setUpdateTime(LocalDateTime.now());
        return pointsRecordMapper.insert(pointsRecord);
    }

    @Transactional
    @Override
    public int updateMerchantpointsRecord(Points2voucherDTO voucherDTO) {
        Integer merId = voucherDTO.getMerId();
        Float points = voucherDTO.getPoints();
        MerchantPoints merchantpoints = new MerchantPoints();
        merchantpoints.setMerId(merId);
        merchantpoints.setPoints(-points);
        merchantpointsMapper.updateMlpoints(merchantpoints);
        PointsRecord pointsRecord = new PointsRecord();
        pointsRecord.setOrderNo("兑换" + merId);
        pointsRecord.setUserId(merId.toString());
        pointsRecord.setPoints(-points);
        pointsRecord.setDescription("兑换代金券" + points + "积分");
        pointsRecord.setType(0);
        pointsRecord.setStatus(1);
        pointsRecord.setUserType(1);
        pointsRecord.setCreateTime(LocalDateTime.now());
        pointsRecord.setUpdateTime(LocalDateTime.now());
        return pointsRecordMapper.insert(pointsRecord);
    }
    @Override
    public int insertOfflineOrderScore(List<OfflineOrder> offlineOrders) {
        rate = Integer.parseInt(sysConfigMapper.selectValueByCode(rateConfig));
        String totalPeriods = sysConfigMapper.selectValueByCode(dictKey);
        LocalDateTime now = LocalDateTime.now();
        List<Userpoints> ups=new ArrayList<>();
        List<MerchantPoints> mps=new ArrayList<>();
        List<Userpoints> uGetps=new ArrayList<>();
        List<MerchantPoints> mGetps=new ArrayList<>();
        List<PointsRecord> pointsRecords = new ArrayList<>();
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyyMMdd");
        int dateChar = Integer.parseInt(now.format(formatter));
        List<PendingPointsDto> pendingPointsDtoList =new ArrayList<>();
        for (OfflineOrder of : offlineOrders) {
            int i = ifExist(of.getOrderCode());
            if (i > 0) {
                continue;
            }
            Integer serviceProviderId = of.getServiceProviderId();
            Long merId = of.getMerId();
            BigDecimal paymentAmount = of.getPaymentAmount();
            BigDecimal commissionRate = of.getCommissionRate().divide(BigDecimal.valueOf(100),10, RoundingMode.DOWN);
            BigDecimal pointsDecimal = commissionRate.multiply(paymentAmount).multiply(BigDecimal.valueOf(rate)).setScale(2, RoundingMode.UP);
            float points = pointsDecimal.floatValue();
            if (points < 0.01f) {
                points = 0.01f;
            }
            Integer uid = of.getUid();
            
            // 商家积分（始终插入）
            MerchantPoints merchantpoints = new MerchantPoints();
            merchantpoints.setMerId(Math.toIntExact(merId));
            merchantpoints.setPoints(points);
            merchantpoints.setPointsGetCount(BigDecimal.valueOf(points));
            mps.add(merchantpoints);
            mGetps.add(merchantpoints);
            // 商家积分记录
            PointsRecord pointsMerchantRecord = new PointsRecord();
            pointsMerchantRecord.setOrderNo(of.getOrderCode());
            pointsMerchantRecord.setUserId(merId.toString());
            pointsMerchantRecord.setPoints(points);
            pointsMerchantRecord.setDescription("用户下单获得积分");
            pointsMerchantRecord.setType(1);
            pointsMerchantRecord.setUserType(1);
            pointsMerchantRecord.setStatus(1);
            pointsMerchantRecord.setTotalPeriods(totalPeriods);
            pointsMerchantRecord.setCreateTime(now);
            pointsMerchantRecord.setUpdateTime(now);
            pointsMerchantRecord.setDateChar(dateChar);
            pointsMerchantRecord.setOrderType(0);
            pointsMerchantRecord.setInvitorTag(0);
            pointsRecords.add(pointsMerchantRecord);
            //商户待统计积分信息
            PendingPointsDto merDto = new PendingPointsDto();
            merDto.setUserType(1);
            merDto.setUserId(merId);
            merDto.setPoints(BigDecimal.valueOf(points));
            merDto.setBatchNo(dateChar);
            merDto.setPeriodNum(Integer.parseInt(totalPeriods));
            merDto.setCreateTime(now);
            pendingPointsDtoList.add(merDto);
            // 检查用户是否有线上账号（通过account字段判断）
            if (of.getAccount() != null && !of.getAccount().trim().isEmpty()) {
                // 用户积分
                Userpoints user = new Userpoints();
                user.setUid(uid);
                user.setPoints(points);
                user.setPointsGetCount(BigDecimal.valueOf(points));
                ups.add(user);
                uGetps.add(user);
                // 用户积分记录
                PointsRecord pointsRecord = new PointsRecord();
                pointsRecord.setOrderNo(of.getOrderCode());
                pointsRecord.setUserId(uid.toString());
                pointsRecord.setPoints(points);
                pointsRecord.setDescription("下单赠送积分");
                pointsRecord.setType(1);
                pointsRecord.setStatus(1);
                pointsRecord.setUserType(0);
                pointsRecord.setCreateTime(now);
                pointsRecord.setTotalPeriods(totalPeriods);
                pointsRecord.setUpdateTime(now);
                pointsRecord.setOrderType(0);
                pointsRecord.setInvitorTag(0);
                pointsRecord.setDateChar(dateChar);
                pointsRecords.add(pointsRecord);
                //用户待统计积分信息
                PendingPointsDto userDto = new PendingPointsDto();
                userDto.setUserType(0);
                userDto.setUserId(Long.valueOf(uid));
                userDto.setPoints(BigDecimal.valueOf(points));
                userDto.setBatchNo(dateChar);
                userDto.setPeriodNum(Integer.parseInt(totalPeriods));
                userDto.setCreateTime(now);
                pendingPointsDtoList.add(userDto);
                // 推荐人积分
                if (serviceProviderId != null && serviceProviderId!= 0) {
                    PointsRecord pointsSpreadRecord = new PointsRecord();
                    BigDecimal sPointsDecimal = new BigDecimal(Float.toString(points)).multiply(new BigDecimal("0.05")).setScale(2, RoundingMode.UP);
                    float SPoints = sPointsDecimal.floatValue();
                    if (SPoints < 0.01f) {
                        SPoints = 0.01f;
                    }
                    if (of.getServiceProviderType() == 0) {
                        //待统计积分
                        PendingPointsDto spreadUserDto = new PendingPointsDto();
                        spreadUserDto.setUserType(0);
                        spreadUserDto.setUserId(Long.valueOf(serviceProviderId));
                        spreadUserDto.setPoints(BigDecimal.valueOf(SPoints));
                        spreadUserDto.setBatchNo(dateChar);
                        spreadUserDto.setPeriodNum(Integer.parseInt(totalPeriods));
                        spreadUserDto.setCreateTime(now);
                        pendingPointsDtoList.add(spreadUserDto);
                        //积分更新
                        Userpoints spreadUser = new Userpoints();
                        spreadUser.setUid(serviceProviderId);
                        spreadUser.setPoints(SPoints);
                        spreadUser.setPointsGetCount(BigDecimal.valueOf(SPoints));
                        pointsSpreadRecord.setUserType(0);
                        ups.add(spreadUser);
                        uGetps.add(spreadUser);
                    } else if (of.getServiceProviderType() == 1) {
                        //待统计积分
                        PendingPointsDto spreadMerDto = new PendingPointsDto();
                        spreadMerDto.setUserType(1);
                        spreadMerDto.setUserId(Long.valueOf(serviceProviderId));
                        spreadMerDto.setPoints(BigDecimal.valueOf(SPoints));
                        spreadMerDto.setBatchNo(dateChar);
                        spreadMerDto.setPeriodNum(Integer.parseInt(totalPeriods));
                        spreadMerDto.setCreateTime(now);
                        pendingPointsDtoList.add(spreadMerDto);
                        //积分更新
                        MerchantPoints spreadMerchant = new MerchantPoints();
                        spreadMerchant.setMerId(serviceProviderId);
                        spreadMerchant.setPoints(SPoints);
                        spreadMerchant.setPointsGetCount(BigDecimal.valueOf(SPoints));
                        pointsSpreadRecord.setUserType(1);
                        mps.add(spreadMerchant);
                        mGetps.add(spreadMerchant);
                    }
                    pointsSpreadRecord.setOrderNo(of.getOrderCode());
                    pointsSpreadRecord.setUserId(serviceProviderId.toString());
                    pointsSpreadRecord.setPoints(SPoints);
                    pointsSpreadRecord.setDescription("邀请得积分");
                    pointsSpreadRecord.setType(1);
                    pointsSpreadRecord.setStatus(1);
                    pointsSpreadRecord.setCreateTime(now);
                    pointsSpreadRecord.setUpdateTime(now);
                    pointsSpreadRecord.setDateChar(dateChar);
                    pointsSpreadRecord.setTotalPeriods(totalPeriods);
                    pointsSpreadRecord.setOrderType(0);
                    pointsSpreadRecord.setInvitorTag(1);
                    pointsRecords.add(pointsSpreadRecord);
                }
            }
        }
        
        try {
            if (!ups.isEmpty()) {
                mlUserMapper.batchUpdateMlUsers(ups);
                mlUserMapper.batchUpdateUserGetPoints(uGetps);
            }
            if (!mps.isEmpty()) {
                merchantpointsMapper.batchUpdateMerchantPoints(mps);
                merchantpointsMapper.batchUpdateMerchantPointsGetCount(mGetps);
            }
            if (!pendingPointsDtoList.isEmpty()) {
                pendingPointsMapper.BatchInsertPendingPoints(pendingPointsDtoList);
            }
            if (!pointsRecords.isEmpty()) {
                return pointsRecordMapper.insertList(pointsRecords);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return 0;
    }
 private int ifExist(String orderCode){
        return pointsRecordMapper.ifExist(orderCode);
 }
 @Override
 public List<PointsRecordVo> getPointsList(PointsRecordQuery query) {
     if (query == null) {
         query = new PointsRecordQuery();
         query.setPageNum(1); // 统一设置默认页码为1
         query.setPageSize(10);
     } else {
         // 设置默认值
         if (query.getPageNum() == null || query.getPageNum() <= 0) {
             query.setPageNum(1);
         }
         if (query.getPageSize() == null || query.getPageSize() <= 0) {
             query.setPageSize(10);
         }

     }
        return pointsRecordMapper.selectPointsRecordList(query);
    }

    @Override
    public int getPointsListCount(PointsRecordQuery query) {
        return pointsRecordMapper.selectPointsRecordListCount(query);
    }

}
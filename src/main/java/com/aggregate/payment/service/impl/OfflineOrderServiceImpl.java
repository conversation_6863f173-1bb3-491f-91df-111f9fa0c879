package com.aggregate.payment.service.impl;

import com.aggregate.payment.dto.OfflineOrderDto;
import com.aggregate.payment.entity.*;
import com.aggregate.payment.entity.payment.*;
import com.aggregate.payment.mapper.*;
import com.aggregate.payment.service.IOfflineOrderService;
import com.aggregate.payment.util.HttpClientUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Base64;
import java.util.List;
import java.math.RoundingMode;



/**
 * 线下订单服务实现类
 * 
 * <AUTHOR>
 */
@Slf4j
@Service
public class OfflineOrderServiceImpl extends ServiceImpl<OfflineOrderMapper, OfflineOrder> implements IOfflineOrderService {
    @Value("${aggregate.payment.wechat.app-id}")
    private String wechatAppId;
    @Value("${aggregate.payment.bank-id}")
    private String bankId;
    @Value("${aggregate.payment.dictKey}")
    private String dictKey;
    @Value("${aggregate.payment.alipay.app-id}")
    private String alipayAppId;
    @Value("${aggregate.payment.payurl}")
    private String baseUrl;
    @Value("${aggregate.payment.refundUrl}")
    private String refundUrl;
    @Value("${jiguang.api.app-key}")
    private String PushAppKey;

    @Value("${jiguang.api.master-secret}")
    private String PushMasterSecret;
    @Autowired
    private OfflineOrderMapper offlineOrderMapper;

    @Autowired
    private MerchantpointsMapper merchantpointsMapper;
    @Autowired
    private AggregateQrCodeMapper aggregateQrCodeMapper;

    @Autowired
    private SysConfigMapper sysConfigMapper;
    @Autowired
    private OfflineUserMapper offlineUserMapper;
    @Autowired
    private MlUserMapper mlUserMapper;



    /**
     * 查询线下订单列表
     *
     * @param offlineOrder 查询条件
     * @return 线下订单列表
     */
    @Override
    public List<OfflineOrder> selectOfflineOrderList(OfflineOrder offlineOrder) {
        return offlineOrderMapper.selectOfflineOrderList(offlineOrder);
    }
    @Override
    public List<OfflineOrder> selectOfflineOrderListTemp(OfflineOrder offlineOrder) {
        return offlineOrderMapper.selectOfflineOrderListTemp(offlineOrder);
    }
    @Override
    public Integer selectOfflineOrderListTempCount(OfflineOrder offlineOrder) {
        return offlineOrderMapper.selectOfflineOrderListTempCount(offlineOrder);
    }

    /**
     * 根据ID查询线下订单
     *
     * @param id 订单ID
     * @return 线下订单信息
     */
    @Override
    public OfflineOrder selectOfflineOrderById(Long id) {
        return offlineOrderMapper.selectOfflineOrderById(id);
    }

    /**
     * 新增线下订单
     *
     * @param offlineOrder 线下订单信息
     * @return 影响行数
     */
    @Override
    public int insertOfflineOrder(OfflineOrder offlineOrder) {
        offlineOrder.setCreateTime(LocalDateTime.now());
        offlineOrder.setUpdateTime(LocalDateTime.now());
        return offlineOrderMapper.insertOfflineOrder(offlineOrder);
    }

    /**
     * 修改线下订单
     *
     * @param offlineOrder 线下订单信息
     * @return 影响行数
     */
    @Override
    public int updateOfflineOrder(OfflineOrder offlineOrder) {
        offlineOrder.setUpdateTime(LocalDateTime.now());
        return offlineOrderMapper.updateOfflineOrder(offlineOrder);
    }

    /**
     * 根据商户ID查询订单
     *
     * @param merId 商户ID
     * @return 线下订单列表
     */
    @Override
    public List<OfflineOrder> selectOrdersByMerId(Long merId) {
        return offlineOrderMapper.selectOrdersByMerId(merId);
    }

    /**
     * 根据购买者openid查询订单
     *
     * @param buyerOpenid 购买者openid
     * @return 线下订单列表
     */
    @Override
    public List<OfflineOrder> selectOrdersByBuyerOpenid(String buyerOpenid) {
        return offlineOrderMapper.selectOrdersByBuyerOpenid(buyerOpenid);
    }

    /**
     * 创建线下订单
     *
     * @param offlineOrderDto 线下订单信息，需包含商户ID(merId)、购买者openid(buyerOpenid)、
     *                    商品名称(productName)、商品描述(productDescription)和支付金额(paymentAmount)
     * @return 创建成功的订单信息
     */
    @Override
    @Transactional
    public PayRespData createOrder(OfflineOrderDto offlineOrderDto) {
        OfflineOrder offlineOrder = new OfflineOrder();
        String openid = offlineOrderDto.getBuyerOpenid();
        BeanUtils.copyProperties(offlineOrderDto, offlineOrder);
        OfflineUser offlineUser = new OfflineUser();
        offlineUser.setOpenId(openid);
        List<OfflineUser> offlineUsers = offlineUserMapper.selectUserScoreList(offlineUser);

            String phone = offlineUsers.get(0).getPhone();
            if (phone != null && !phone.isEmpty()) {
                offlineOrder.setAccount(phone);
                Userpoints userpoints = mlUserMapper.selectMlUserByAccount(phone);
                if (userpoints != null) {
                    offlineOrder.setUid(userpoints.getUid());
                    offlineOrder.setServiceProviderId(userpoints.getServiceProviderId());
                    offlineOrder.setServiceProviderType(userpoints.getServiceProviderType());
                }
            }

        log.info("开始创建订单: qrcodeKey={}, openid={}", offlineOrderDto.getQrcodeKey(), openid);

        try {
            // 1. 查询二维码
            LambdaQueryWrapper<AggregateQrCode> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AggregateQrCode::getQrCodeKey, offlineOrderDto.getQrcodeKey());
            AggregateQrCode qrCode = aggregateQrCodeMapper.selectOne(wrapper);

            if (qrCode == null) {
                log.error("二维码不存在: qrcodeKey={}", offlineOrderDto.getQrcodeKey());
                throw new RuntimeException("二维码不存在");
            }
            log.info("二维码查询成功: merchantId={}", qrCode.getMerchantId());

            // 2. 查询平台银行配置
            String platBank = sysConfigMapper.selectValueByCode(dictKey);
            log.info("平台银行配置: {}", platBank);

            // 3. 查询商户信息
            Long merId = qrCode.getMerchantId();
            MerchantPoints merchantpoints = merchantpointsMapper.selectUserpoints(Math.toIntExact(merId));
            if (merchantpoints == null) {
                log.error("商户不存在: merId={}", merId);
                throw new RuntimeException("商户不存在");
            }
            log.info("商户查询成功: bankUserCode={}", merchantpoints.getBankUserCode());

            // 4. 计算分佣金额
            BigDecimal paymentAmount = offlineOrder.getPaymentAmount();
            BigDecimal commissionRate = merchantpoints.getCommissionRate();
            BigDecimal commissionPl = paymentAmount.multiply(commissionRate).multiply(BigDecimal.valueOf(0.01)).setScale(2, RoundingMode.DOWN);
            BigDecimal commissionMer = paymentAmount.subtract(commissionPl).setScale(2, RoundingMode.DOWN);
            log.info("分佣计算成功: commissionPl={}, commissionMer={}", commissionPl, commissionMer);

            // 5. 生成订单号
            int totalLength = 32;
            long currentTimeMillis = System.currentTimeMillis();
            String timePart = String.valueOf(currentTimeMillis); // 13 位毫秒时间戳
            int remainingLength = totalLength - timePart.length();
            String idPart;
            if (openid.length() <= remainingLength) {
                idPart = openid;
            } else {
                idPart = openid.substring(openid.length() - remainingLength);
            }
            offlineOrder.setOrderCode(idPart + timePart);
            log.info("订单号生成成功: {}", offlineOrder.getOrderCode());

            // 6. 设置订单其他属性
            offlineOrder.setMerId(merId);
            offlineOrder.setCommissionRate(commissionRate);
            offlineOrder.setBuyerOpenid(openid);
            offlineOrder.setProductName("用户id" + openid + LocalDateTime.now() + "补差价");
            offlineOrder.setProductDescription(merchantpoints.getMerName() + offlineOrderDto.getPaymentAmount().multiply(BigDecimal.valueOf(1000)) + "补差价");
            offlineOrder.setPaymentAmount(paymentAmount);
            offlineOrder.setPayState(0); // 未支付
            offlineOrder.setOrderState(0); // 未完成
            offlineOrder.setCommissionMer(commissionMer);
            offlineOrder.setCommissionPl(commissionPl);
            offlineOrder.setCreateTime(LocalDateTime.now());
            offlineOrder.setUpdateTime(LocalDateTime.now());
            offlineOrder.setBankUserCode(merchantpoints.getBankUserCode());
            offlineOrder.setPlatBankCode(platBank);
            offlineOrder.setMrchSno(merchantpoints.getBankUserCode());

            log.info("订单属性设置成功");

            // 7. 插入订单
            int result = offlineOrderMapper.insertOfflineOrder(offlineOrder);
            int i = offlineOrderMapper.insertOfflineOrderTemp(offlineOrder);
            BeanUtils.copyProperties(offlineOrder, offlineOrderDto);
            offlineOrderDto.setMerName(merchantpoints.getMerName());
            log.info("发送前获取商家id: {}", offlineOrderDto.getMerId());

            // 8. 发送支付请求
            ResponseData responseData = SendPayRequest(offlineOrderDto);
            if (responseData.getErrorCode().equals("000000")) {
                Object rsp = responseData.getRspData();
                if (rsp != null) {
                    log.info("创建线下订单成功: orderId={}, merId={}, amount={}",
                            offlineOrder.getId(), merId, paymentAmount);
                    try {
                        if (offlineOrder.getPayType().equals("THS_WX")){
                            PayRespData payRespData = JSON.parseObject(rsp.toString(), PayRespData.class);
                            payRespData.setAppId(wechatAppId);
                            return payRespData;
                        } else {
                            PayRespData payRespData = JSON.parseObject(rsp.toString(), PayRespData.class);
                            return payRespData;
                        }

                    } catch (Exception e) {
                        log.error("解析响应数据出错", e);
                        throw new RuntimeException("创建订单失败");
                    }
                } else {
                    log.error("支付请求成功但响应数据为空");
                    throw new RuntimeException("创建订单失败：响应数据为空");
                }
            } else {
                log.info("支付请求返回数据: {}", JSON.toJSONString(responseData));
                throw new RuntimeException("创建订单失败");
            }

        } catch (Exception e) {
            log.error("创建线下订单失败: merId={}, amount={}, error={}",
                     offlineOrder.getMerId(), offlineOrder.getPaymentAmount(), e.getMessage(), e);
            throw new RuntimeException("创建订单失败: " + e.getMessage());
        }
    }

    /**
     * 更新订单支付状态
     *
     * @param offlineOrder 线下订单信息，需包含订单ID(id)和支付状态(payState)
     * @return 是否更新成功
     */
    @Override
    @Transactional
    public boolean updatePayState(OfflineOrder offlineOrder) {
        try {
            Long orderId = offlineOrder.getId();
            Integer payState = offlineOrder.getPayState();

            // 只更新必要的字段
            OfflineOrder updateOrder = new OfflineOrder();
            updateOrder.setId(orderId);
            updateOrder.setPayState(payState);
            updateOrder.setUpdateTime(LocalDateTime.now());

            int result = offlineOrderMapper.updateOfflineOrder(updateOrder);
            if (result > 0) {
                log.info("更新订单支付状态成功: orderId={}, payState={}", orderId, payState);
                return true;
            }
            return false;

        } catch (Exception e) {
            log.error("更新订单支付状态失败: orderId={}, payState={}, error={}",
                     offlineOrder.getId(), offlineOrder.getPayState(), e.getMessage(), e);
            return false;
        }
    }

    /**
     * 更新订单状态
     *
     * @param offlineOrder 线下订单信息，需包含订单ID(id)和订单状态(orderState)
     * @return 是否更新成功
     */
    @Override
    @Transactional
    public boolean updateOrderState(OfflineOrder offlineOrder) {
        try {
            Long orderId = offlineOrder.getId();
            Integer orderState = offlineOrder.getOrderState();

            // 只更新必要的字段
            OfflineOrder updateOrder = new OfflineOrder();
            updateOrder.setId(orderId);
            updateOrder.setOrderState(orderState);
            updateOrder.setUpdateTime(LocalDateTime.now());

            int result = offlineOrderMapper.updateOfflineOrder(updateOrder);
            if (result > 0) {
                log.info("更新订单状态成功: orderId={}, orderState={}", orderId, orderState);
                return true;
            }
            return false;

        } catch (Exception e) {
            log.error("更新订单状态失败: orderId={}, orderState={}, error={}",
                     offlineOrder.getId(), offlineOrder.getOrderState(), e.getMessage(), e);
            return false;
        }
    }

    @Override
    public int selectOfflineOrderCount(OfflineOrder offlineOrder) {
       return offlineOrderMapper.selectOfflineOrderCount(offlineOrder);
    }

    private PayDto buildPayDto(OfflineOrderDto offlineOrderDto){
        PayDto payDto = new PayDto();
        ArrayList<PayInfoDto> payInfoDtos = new ArrayList<>();
        ArrayList<MatchInfoDto> matchInfoDtos = new ArrayList<>();
        // 平台分佣
        MatchInfoDto matchInfoDto1 = new MatchInfoDto();
        matchInfoDto1.setSaFlg("1");
        matchInfoDto1.setSaAmt(String.valueOf(offlineOrderDto.getCommissionPl()));
        matchInfoDto1.setSaPsnNm(offlineOrderDto.getPlatBankCode());
        matchInfoDtos.add(matchInfoDto1);

        // 商户分佣
        MatchInfoDto matchInfoDto2 = new MatchInfoDto();
        matchInfoDto2.setSaFlg("2");
        matchInfoDto2.setSaAmt(String.valueOf(offlineOrderDto.getCommissionMer()));
        matchInfoDto2.setSaPsnNm(offlineOrderDto.getBankUserCode());
        matchInfoDtos.add(matchInfoDto2);

        //支付信息设置 暂时微信
        if (offlineOrderDto.getPayType().equals("THS_WX")){
            PayInfoDto payInfoDto = new PayInfoDto();
            payInfoDto.setLoginNo(wechatAppId);
//            payInfoDto.setClntId(offlineOrderDto.getBuyerOpenid());
            log.info("购买者openid{}",offlineOrderDto.getBuyerOpenid());
            payInfoDto.setClntSbtpId(offlineOrderDto.getBuyerOpenid());
//            payInfoDto.setChannelCode("THS_WX");
            payInfoDtos.add(payInfoDto);
            payDto.setPayType("THS_WX");
        }
        if (offlineOrderDto.getPayType().equals("THS_ALI")){
            PayInfoDto payInfoDto = new PayInfoDto();
            payInfoDto.setLoginNo(alipayAppId);
//            payInfoDto.setClntId(offlineOrderDto.getBuyerOpenid());
            payInfoDto.setClntSbtpId(offlineOrderDto.getBuyerOpenid());
            payInfoDto.setChannelCode("THS_ALI");
            payInfoDtos.add(payInfoDto);
            payDto.setPayType("THS_ALI");
        }

        //请求实体类设置
        payDto.setPyInsrInfoArray(payInfoDtos);
        payDto.setDsc(offlineOrderDto.getProductDescription());
        payDto.setIp(offlineOrderDto.getIp());
        payDto.setMechNo(offlineOrderDto.getMrchSno());
        payDto.setPaymentFlag("1");
        payDto.setTitle(offlineOrderDto.getProductName());
        //可能坑,decimal转string
        payDto.setTranAmt(String.valueOf(offlineOrderDto.getPaymentAmount()));
        payDto.setWsId(offlineOrderDto.getWsId());
        payDto.setMac(offlineOrderDto.getMac());
        payDto.setPyInsrInfoArray(payInfoDtos);
        payDto.setElmtMatchInfoArray(matchInfoDtos);
        payDto.setTxTime(String.valueOf(offlineOrderDto.getCreateTime()));
//        payDto.setTxSno(offlineOrderDto.getMrchSno());
        payDto.setMrchSno(offlineOrderDto.getOrderCode());
        return payDto;
    }
    private ResponseData SendPayRequest(OfflineOrderDto offlineOrderDto) {
        // 构建PayDto对象

        PayDto payDto = buildPayDto(offlineOrderDto);
        String mechNo = payDto.getMechNo();
        log.info("<UNK>: mechNo={}", mechNo);
        // 构建请求URL（带查询参数）
        String fullUrl = baseUrl + "?bankId=" + bankId;

        try {
            // 发起POST请求
            String response = HttpClientUtil.post(fullUrl)
                    .jsonBody(payDto)
                    .connectTimeout(30000)
                    .readTimeout(30000)
                    .executeAsString();

            log.info("支付请求响应: {}", response);

            // 解析响应
            return JSON.parseObject(response, ResponseData.class);

        } catch (Exception e) {
            log.error("发送支付请求失败", e);
            throw new RuntimeException("支付请求失败: " + e.getMessage());
        }
    }
    @Override
    public ResponseData RefundOfflineOrder(OfflineOrder offlineOrderDto) {
        offlineOrderDto = offlineOrderMapper.selectOfflineOrderByOrderCode(offlineOrderDto.getOrderCode());
        // 构建请求URL（带查询参数）
        String fullUrl = refundUrl + "?bankId=" + bankId;
        RefundPayInfo refundPayInfo = new RefundPayInfo();
        refundPayInfo.setMechNo(offlineOrderDto.getBankUserCode());
        refundPayInfo.setOriThirdSeqNo(offlineOrderDto.getOrderCode());
        refundPayInfo.setRefundAmt(String.valueOf(offlineOrderDto.getPaymentAmount()));
        refundPayInfo.setChannelCode(offlineOrderDto.getPayType());
        ArrayList<MatchInfoDto> matchInfoDtos = new ArrayList<>();
        //构建商户退款信息
        MatchInfoDto matchInfoDto2 = new MatchInfoDto();
        matchInfoDto2.setSaFlg("2");
        matchInfoDto2.setSaAmt(String.valueOf(offlineOrderDto.getCommissionMer()));
        matchInfoDto2.setSaPsnNm(offlineOrderDto.getBankUserCode());
        matchInfoDtos.add(matchInfoDto2);
        //构建平台退款信息
        MatchInfoDto matchInfoDto1 = new MatchInfoDto();
        matchInfoDto1.setSaFlg("1");
        matchInfoDto1.setSaAmt(String.valueOf(offlineOrderDto.getCommissionPl()));
        matchInfoDto1.setSaPsnNm(offlineOrderDto.getPlatBankCode());
        matchInfoDtos.add(matchInfoDto1);
        refundPayInfo.setIp(offlineOrderDto.getIp());
        refundPayInfo.setMac(offlineOrderDto.getMac());
        refundPayInfo.setElmtMatchInfoArray(matchInfoDtos);
        refundPayInfo.setWsId(offlineOrderDto.getWsId());
        refundPayInfo.setTxTime(String.valueOf(offlineOrderDto.getCreateTime()));
        try {
            // 发起POST请求
            String response = HttpClientUtil.post(fullUrl)
                    .jsonBody(refundPayInfo)
                    .connectTimeout(30000)
                    .readTimeout(30000)
                    .executeAsString();

            log.info("退款请求响应: {}", response);
            // 解析响应
            ResponseData responseData = JSON.parseObject(response, ResponseData.class);
            if (responseData.getErrorCode().equals("000000")){
                offlineOrderMapper.deleteRefundTempOrder(offlineOrderDto.getOrderCode());
                offlineOrderMapper.refundOrderState(offlineOrderDto.getOrderCode());
                return responseData;
            }else {
                log.error("退款失败{}",responseData.getErrorMsg());
                return responseData;
            }
        } catch (Exception e) {
            log.error("发送退款请求失败", e);
            throw new RuntimeException("退款请求失败: " + e.getMessage());
        }

    }
    public void payConfirmCallBack(){
        try{
        // 1. 构建请求URL
        String url = "https://api.jpush.cn/v3/push";

        // 2. 创建HttpClient
        CloseableHttpClient client = HttpClients.createDefault();

        // 3. 创建POST请求
        HttpPost post = new HttpPost(url);

        // 4. 设置请求头 Authorization
        String auth = PushAppKey + ":" + PushMasterSecret;
        String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
        post.setHeader("Authorization", "Basic " + encodedAuth);
        post.setHeader("Content-Type", "application/json");

        // 5. 构建请求体 JSON
        JSONObject body = new JSONObject();
        body.put("platform", "all");

        // audience 设置为标签
        JSONObject audience = new JSONObject();
        audience.put("registration_id", new String[]{"18071adc022b5c41fb0"});
        body.put("audience", audience);

        // notification 设置
        JSONObject notification = new JSONObject();
        notification.put("alert", "Hello, JPush!");

        // Android 通知
        JSONObject android = new JSONObject();
        android.put("alert", "Hi, Android!");
        android.put("title", "您有一条线下订单支付成功");
        android.put("builder_id", 1);
        android.put("extras", new JSONObject().put("key", "value"));
        android.put("sound","default");

        // iOS 通知
        JSONObject ios = new JSONObject();
        ios.put("alert", "Hi, iOS!");
        ios.put("sound", "default");
        ios.put("badge", "+1");
        ios.put("extras", new JSONObject().put("key", "value"));

        notification.put("android", android);
        notification.put("ios", ios);
        body.put("notification", notification);

        // options 设置
        JSONObject options = new JSONObject();
        options.put("time_to_live", 60);
        options.put("apns_production", false);
        body.put("options", options);

        // 6. 设置请求体
        StringEntity entity = new StringEntity(body.toString(), "UTF-8");
        entity.setContentType("application/json");
        post.setEntity(entity);

        // 7. 发送请求
        HttpResponse response = client.execute(post);

        // 8. 获取响应
        String responseString = EntityUtils.toString(response.getEntity(), "UTF-8");
        System.out.println("响应结果：\n" + responseString);

    } catch (Exception e) {
        e.printStackTrace();

        }
    }

}
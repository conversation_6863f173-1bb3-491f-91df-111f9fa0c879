package com.aggregate.payment.service.impl;

import com.aggregate.payment.entity.OfflineOrder;
import com.aggregate.payment.entity.TOrderStatistics;
import com.aggregate.payment.entity.ThreadPool.ThreadPoolRequest;
import com.aggregate.payment.entity.payment.MatchInfoDto;
import com.aggregate.payment.entity.payment.PayConfirmDto;
import com.aggregate.payment.entity.payment.ResponseData;
import com.aggregate.payment.mapper.OfflineOrderMapper;
import com.aggregate.payment.mapper.TOrderStatisticsMapper;
import com.aggregate.payment.service.PointsRecordService;
import com.aggregate.payment.util.HttpClientUtil;
import com.alibaba.fastjson2.JSON;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;
import java.util.stream.Collectors;

@Service
public class ThreadTaskService {
    @Autowired
    private OfflineOrderMapper offlineOrderMapper;
    @Autowired
    private PointsRecordService pointsRecordService;
    @Value("${aggregate.payment.bank-id}")
    private String bankId;
    @Value("${aggregate.payment.payConfirm}")
    private String baseUrl;
    @Autowired
    private TOrderStatisticsMapper orderStatisticsMapper;
    private static final Logger logger = LoggerFactory.getLogger(ThreadTaskService.class);
    @Async
    public CompletableFuture<Integer> executeTask(ThreadPoolRequest req) {
        int queueCapacity = req.getQueueCapacity() > 0 ? req.getQueueCapacity() : 10;
        int consumerCount = req.getCorePoolSize() > 0 ? req.getCorePoolSize() : 4;
        int batchSize = 1000;
        BlockingQueue<List<OfflineOrder>> queue = new LinkedBlockingQueue<>(queueCapacity);

        List<String> failedOrders = Collections.synchronizedList(new ArrayList<>());
        List<Thread> consumers = new ArrayList<>();

        // 消费者线程
        Runnable consumerTask = () -> {
            while (true) {
                try {
                    List<OfflineOrder> batch = queue.take();
                    if (batch.isEmpty()) break; // 结束标记
                    int processedCount = batchProcess(batch);
                    logger.info("成功处理批次 {} 条记录", processedCount);
                } catch (Exception e) {
                    logger.error("消费者处理批次时发生异常", e);
                    failedOrders.add("Batch-Exception");
                }
            }
        };
        for (int i = 0; i < consumerCount; i++) {
            Thread consumer = new Thread(consumerTask, "Consumer-" + i);
            consumer.start();
            consumers.add(consumer);
        }

        // 生产者线程：一次性查出所有数据，分批放入队列
        Thread producer = new Thread(() -> {
            try {
                List<OfflineOrder> allOrders = offlineOrderMapper.selectOfflineOrderList4Pay(new OfflineOrder());
                int totalItems = allOrders.size();
                int totalPages = (int) Math.ceil(totalItems / (double) batchSize);
                for (int page = 0; page < totalPages; page++) {
                    int fromIndex = page * batchSize;
                    int toIndex = Math.min(fromIndex + batchSize, totalItems);
                    List<OfflineOrder> batch = allOrders.subList(fromIndex, toIndex);
                    queue.put(new ArrayList<>(batch)); // put副本，避免后续subList变动
                }
            } catch (Exception e) {
                logger.error("生产者查库或分批放入队列时发生异常", e);
                failedOrders.add("Producer-Exception");
            } finally {
                // 放入结束标记
                for (int i = 0; i < consumerCount; i++) {
                    try {
                        queue.put(Collections.emptyList());
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }
                }
            }
        }, "Producer");
        producer.start();

        try {
            producer.join();
            for (Thread consumer : consumers) {
                consumer.join();
            }
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }

        if (!failedOrders.isEmpty()) {
            logger.warn("有 {} 个批次处理失败: {}", failedOrders.size(), failedOrders);
            // 可以在这里添加重试逻辑或通知机制
        }
        return CompletableFuture.completedFuture(failedOrders.size());
    }

    /**
     * 单笔订单确认：前端传 orderCode，查库后走批量处理逻辑
     */
    public ResponseData confirmSingleOrder(String orderCode) {
        if (orderCode == null || orderCode.trim().isEmpty()) {
            throw new IllegalArgumentException("orderCode不能为空");
        }
        // 查询单笔订单
        OfflineOrder order = offlineOrderMapper.selectOfflineOrderByOrderCode(orderCode);
        if (order == null) {
            throw new RuntimeException("未找到对应订单: " + orderCode);
        }
        List<OfflineOrder> singleList = Collections.singletonList(order);

        // 复用批量处理逻辑
        int successCount = batchProcess(singleList);
        // 构造返回结果
        ResponseData resp = new ResponseData();
        if (successCount > 0) {
            resp.setErrorCode("000000");
            resp.setErrorMsg("订单确认成功");
        } else {
            resp.setErrorCode("999999");
            resp.setErrorMsg("订单确认失败");
        }
        return resp;
    }


    private List<PayConfirmDto> buildPayConfirmDto(List<OfflineOrder> offlineOrders) {
        List<PayConfirmDto> payConfirmDtoList = new ArrayList<PayConfirmDto>();
        for (OfflineOrder offlineOrder : offlineOrders) {
            PayConfirmDto payConfirmDto = new PayConfirmDto();
            payConfirmDto.setMechNo(offlineOrder.getBankUserCode());
            payConfirmDto.setOriThirdSeqNo(offlineOrder.getOrderCode());
            payConfirmDto.setTranAmt(String.valueOf(offlineOrder.getPaymentAmount()));
            //设置分发信息
            ArrayList<MatchInfoDto> MatchInfoDtos = new ArrayList<>();
            MatchInfoDto pl = new MatchInfoDto();
            pl.setSaFlg("1");
            pl.setSaAmt(String.valueOf(offlineOrder.getCommissionPl()));
            pl.setSaPsnNm(offlineOrder.getPlatBankCode());
            MatchInfoDtos.add(pl);
            MatchInfoDto mer = new MatchInfoDto();
            mer.setSaFlg("2");
            mer.setSaAmt(String.valueOf(offlineOrder.getCommissionMer()));
            mer.setSaPsnNm(offlineOrder.getBankUserCode());
            MatchInfoDtos.add(mer);
            payConfirmDto.setElmtMatchInfoArray(MatchInfoDtos);
            payConfirmDto.setIp(offlineOrder.getIp());
            payConfirmDto.setMac(offlineOrder.getMac());
            payConfirmDto.setWsId(offlineOrder.getWsId());
            payConfirmDto.setTxTime(String.valueOf(offlineOrder.getCreateTime()));
//            payConfirmDto.setTxSno(offlineOrder.getMrchSno());
//            payConfirmDto.setMrchSno(offlineOrder.getOrderCode());
            payConfirmDtoList.add(payConfirmDto);

        }
        return payConfirmDtoList;
    }

    public ResponseData sendPayConfirmRequest(PayConfirmDto payConfirmDto) {
        // 构建请求URL（带查询参数）

        String fullUrl = baseUrl + "?bankId=" + bankId;

        try {
            // 发起POST请求
            String response = HttpClientUtil.post(fullUrl)
                    .jsonBody(payConfirmDto)
                    .connectTimeout(30000)
                    .readTimeout(30000)
                    .executeAsString();

            logger.info("支付确认请求响应: {}", response);

            // 解析响应
            return JSON.parseObject(response, ResponseData.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    @Transactional // 确保在事务中执行
    public int batchProcess(List<OfflineOrder> offlineOrders) {
        List<PayConfirmDto> payConfirmDtoList = buildPayConfirmDto(offlineOrders);
        List<String> successOrders = new ArrayList<>();
        List<String> errorOrders = new ArrayList<>();
        for (PayConfirmDto payConfirmDto : payConfirmDtoList) {
            try {
                ResponseData responseData = sendPayConfirmRequest(payConfirmDto);
                if (responseData != null && "000000".equals(responseData.getErrorCode())) {
//                if (1==1){
                    logger.info("支付确认成功: {}", payConfirmDto.getOriThirdSeqNo());
                    String oriThirdSeqNo = payConfirmDto.getOriThirdSeqNo();
                    successOrders.add(oriThirdSeqNo);
                } else {
                    errorOrders.add(payConfirmDto.getOriThirdSeqNo());
                    logger.warn("支付确认失败: {}, 响应: {}", payConfirmDto.getOriThirdSeqNo(), responseData);
                }
            } catch (Exception e) {

                logger.error("处理订单 {} 时发生异常", payConfirmDto.getOriThirdSeqNo(), e);
            }
        }
        if (!successOrders.isEmpty()) {
            offlineOrderMapper.deleteSuccessOrdersTemp(successOrders);
            offlineOrderMapper.updateOrderState(successOrders);
            // 1. 所有成功的订单都插入统计数据（不过滤account）
            List<OfflineOrder> successOrderEntities = offlineOrders.stream()
                    .filter(order -> successOrders.contains(order.getOrderCode()))
                    .collect(Collectors.toList());
            if (successOrderEntities.size() > 0) {
                batchInsertStaticOrders(successOrderEntities);
                pointsRecordService.insertOfflineOrderScore(successOrderEntities);
            }
        }if (!errorOrders.isEmpty()) {
            int i1 = offlineOrderMapper.updateErrStateIntTemp(errorOrders);
            int i = offlineOrderMapper.updateErrStateInt(errorOrders);
            logger.info("更新失败订单数: {}", i);
            logger.info("临时单更新数目: {}",i1);


        }

        return successOrders.size();
    }
    private void batchInsertStaticOrders(List<OfflineOrder> offlineOrders) {
           List<TOrderStatistics> statistics =new ArrayList<>();
        for (OfflineOrder offlineOrder : offlineOrders) {
            Long merId = offlineOrder.getMerId();
            BigDecimal paymentAmount = offlineOrder.getPaymentAmount();
            TOrderStatistics orderStatistics = new TOrderStatistics();
            orderStatistics.setMerchantId(merId);
            orderStatistics.setAmount(paymentAmount);
            orderStatistics.setType(1);
            orderStatistics.setTimestamp(System.currentTimeMillis());
            statistics.add(orderStatistics);
        }
        orderStatisticsMapper.insertBatch(statistics);
    }

}
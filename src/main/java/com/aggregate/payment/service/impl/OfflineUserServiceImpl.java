package com.aggregate.payment.service.impl;

import com.aggregate.payment.common.AjaxResult;
import com.aggregate.payment.entity.*;


import com.aggregate.payment.mapper.AggregateQrCodeMapper;
import com.aggregate.payment.mapper.MlOfflineSmsCodeMapper;
import com.aggregate.payment.mapper.MlUserMapper;
import com.aggregate.payment.mapper.OfflineUserMapper;
import com.aggregate.payment.service.IOfflineUserService;
import com.aggregate.payment.util.Base64Util;
import com.aggregate.payment.util.HttpClientUtil;
import com.alibaba.fastjson2.JSON;
import com.alibaba.fastjson2.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.time.LocalDateTime;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Service
public class OfflineUserServiceImpl implements IOfflineUserService {
    @Autowired
    private OfflineUserMapper offlineUserMapper;
    @Autowired
    private MlUserMapper mlUserMapper;
    @Autowired
    private MlOfflineSmsCodeMapper mlOfflineSmsCodeMapper;
    @Autowired
    private AggregateQrCodeMapper aggregateQrCodeMapper;

    @Override
    public int insertUserScore(OfflineUser offlineUser) {

        return offlineUserMapper.insertUserScore(offlineUser);
    }

    @Override
    public List<OfflineUser> getUserScores(OfflineUser offlineUser) {
        return offlineUserMapper.selectUserScoreList(offlineUser);
    }

    @Override
    public int updateUserScore(OfflineUser offlineUser) {
        return offlineUserMapper.updateUserScore(offlineUser);
    }

    @Transactional
    @Override
    public AjaxResult bindPhoneNumber(OfflineUserReInfo offlineUser) {
        String phone = offlineUser.getPhone();
        MlOfflineSmsCode code = mlOfflineSmsCodeMapper.selectLatestByPhone(phone);
        if (code == null) {
            return AjaxResult.error("验证码不存在或已过期");
        }

        // 验证码比较
        String inputCode = offlineUser.getSmsCode();
        if (inputCode == null || inputCode.trim().isEmpty()) {
            return AjaxResult.error("请输入验证码");
        }

        String encodedInputCode = Base64Util.encode(inputCode);
        if (encodedInputCode == null || !encodedInputCode.equalsIgnoreCase(code.getSmsCode())) {
            return AjaxResult.error("验证码错误");
        }

        // 只允许5分钟内的验证码
        if (code.getCreateTime().isBefore(LocalDateTime.now().minusMinutes(5))) {
            return AjaxResult.error("验证码已过期");
        }


        // 建议：加验证码已用标志
        if (code.getUsed()==1) {
            return AjaxResult.error("验证码已被使用");
        }


        // 验证通过，后续绑定逻辑...
        // 更新验证码为已用
        mlOfflineSmsCodeMapper.markUsed(code.getId());

        Userpoints userpoints = new Userpoints();
        userpoints.setAccount(phone);
        Userpoints userpoints1 = mlUserMapper.selectMlUserE(userpoints);
        if (userpoints1 == null) {
            LambdaQueryWrapper<AggregateQrCode> wrapper = new LambdaQueryWrapper<>();
            wrapper.eq(AggregateQrCode::getQrCodeKey, offlineUser.getQrCodeKey());
            AggregateQrCode aggregateQrCode = aggregateQrCodeMapper.selectOne(wrapper);
            Long merchantId = aggregateQrCode.getMerchantId();
            Userpoints us = new Userpoints();
            us.setAccount(phone);
            //设置用户初始密码为123456789a
            us.setPwd("$2y$10$GcUtyaBBne/X0qkYJLS07e9eIfy7deoK12Aqhk0rpyU/6KPzJTfFa");
            us.setPhone(phone);
            us.setNickname(phone.substring(0, 3) + "****" + phone.substring(7));
            us.setSpreadUid(Math.toIntExact(merchantId));
            us.setAvatar("0");
            us.setServiceProviderType(1);
            us.setServiceProviderId(Math.toIntExact(merchantId));
            us.setLastIp(offlineUser.getIp());
            us.setUserType("h5");
            mlUserMapper.insert(us);
        }
        OfflineUser offlineUser1 = new OfflineUser();
        offlineUser1.setPhone(phone);
        offlineUser1.setOpenId(offlineUser.getOpenId());
        offlineUserMapper.updateUserScore(offlineUser1);


        return AjaxResult.success();

    }
    @Override
    public AjaxResult getSms(String phone) {
        Map<String, String> map = new HashMap<>();
        String encode = Base64Util.encode(phone);
        map.put("s_encryp", encode);
        String s = HttpClientUtil.post("https://wjsh.fengzhidi.com/api/openapi/get_code")
                .formBody(map)
                .connectTimeout(30000)
                .readTimeout(30000)
                .executeAsString();

        // 解析JSON响应
        JSONObject jsonObject = JSON.parseObject(s);

        // 检查状态码是否为200
        if (jsonObject != null && jsonObject.getIntValue("status") == 200) {
            // 获取验证码
            JSONObject data = jsonObject.getJSONObject("data");
            if (data != null && data.containsKey("code")) {
                String encodedCode = data.getString("code");
                MlOfflineSmsCode code = new MlOfflineSmsCode();
                code.setPhoneNumber(phone);
                code.setSmsCode(encodedCode);
                code.setUsed(0);
                // 解码Base64验证码
                mlOfflineSmsCodeMapper.insertSmsCode(code);
                return AjaxResult.success("验证码发送成功,5分钟内有效");
            }
        }
        return AjaxResult.error(400, "获取验证码失败");
    }

    /**
     * 佣金结算
     * @param offlineUser
     * @return
     */
    @Override
    public AjaxResult verifyPhone(OfflineUserReInfo offlineUser) {
        String phone = offlineUser.getPhone();
        MlOfflineSmsCode code = mlOfflineSmsCodeMapper.selectLatestByPhone(phone);
        if (code == null) {
            return AjaxResult.error("验证码不存在或已过期");
        }

        // 验证码比较
        String inputCode = offlineUser.getSmsCode();
        if (inputCode == null || inputCode.trim().isEmpty()) {
            return AjaxResult.error("请输入验证码");
        }

        String encodedInputCode = Base64Util.encode(inputCode);
        if (encodedInputCode == null || !encodedInputCode.equalsIgnoreCase(code.getSmsCode())) {
            return AjaxResult.error("验证码错误");
        }

        // 只允许5分钟内的验证码
        if (code.getCreateTime().isBefore(LocalDateTime.now().minusMinutes(5))) {
            return AjaxResult.error("验证码已过期");
        }


        // 建议：加验证码已用标志
        if (code.getUsed()==1) {
            return AjaxResult.error("验证码已被使用");
        }




        // 更新验证码为已用
        mlOfflineSmsCodeMapper.markUsed(code.getId());
        return AjaxResult.success();
    }
}

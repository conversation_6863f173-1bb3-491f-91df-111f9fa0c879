package com.aggregate.payment.util;

import com.google.zxing.BarcodeFormat;
import com.google.zxing.EncodeHintType;
import com.google.zxing.WriterException;
import com.google.zxing.client.j2se.MatrixToImageWriter;
import com.google.zxing.common.BitMatrix;
import com.google.zxing.qrcode.QRCodeWriter;
import lombok.extern.slf4j.Slf4j;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;

/**
 * 二维码工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class QrCodeUtil {

    /** 默认二维码宽度 */
    private static final int DEFAULT_WIDTH = 300;
    
    /** 默认二维码高度 */
    private static final int DEFAULT_HEIGHT = 300;
    
    /** 默认图片格式 */
    private static final String DEFAULT_FORMAT = "PNG";

    /**
     * 生成二维码图片字节数组
     * 
     * @param content 二维码内容
     * @return 图片字节数组
     */
    public static byte[] generateQrCodeImage(String content) {
        return generateQrCodeImage(content, DEFAULT_WIDTH, DEFAULT_HEIGHT);
    }

    /**
     * 生成二维码图片字节数组
     * 
     * @param content 二维码内容
     * @param width 图片宽度
     * @param height 图片高度
     * @return 图片字节数组
     */
    public static byte[] generateQrCodeImage(String content, int width, int height) {
        try {
            // 设置二维码参数
            Map<EncodeHintType, Object> hints = new HashMap<>();
            hints.put(EncodeHintType.CHARACTER_SET, "UTF-8");
            hints.put(EncodeHintType.MARGIN, 1);

            // 生成二维码
            QRCodeWriter qrCodeWriter = new QRCodeWriter();
            BitMatrix bitMatrix = qrCodeWriter.encode(content, BarcodeFormat.QR_CODE, width, height, hints);

            // 转换为BufferedImage
            BufferedImage bufferedImage = MatrixToImageWriter.toBufferedImage(bitMatrix);

            // 转换为字节数组
            ByteArrayOutputStream outputStream = new ByteArrayOutputStream();
            ImageIO.write(bufferedImage, DEFAULT_FORMAT, outputStream);
            
            return outputStream.toByteArray();
            
        } catch (WriterException | IOException e) {
            log.error("生成二维码失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成二维码失败", e);
        }
    }

    /**
     * 生成二维码Base64字符串
     * 
     * @param content 二维码内容
     * @return Base64编码的图片字符串
     */
    public static String generateQrCodeBase64(String content) {
        return generateQrCodeBase64(content, DEFAULT_WIDTH, DEFAULT_HEIGHT);
    }

    /**
     * 生成二维码Base64字符串
     * 
     * @param content 二维码内容
     * @param width 图片宽度
     * @param height 图片高度
     * @return Base64编码的图片字符串
     */
    public static String generateQrCodeBase64(String content, int width, int height) {
        try {
            byte[] imageBytes = generateQrCodeImage(content, width, height);
            return java.util.Base64.getEncoder().encodeToString(imageBytes);
        } catch (Exception e) {
            log.error("生成二维码Base64失败: {}", e.getMessage(), e);
            throw new RuntimeException("生成二维码Base64失败", e);
        }
    }
}

package com.aggregate.payment.util;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.io.PrintWriter;
import java.net.ConnectException;
import java.net.SocketTimeoutException;
import java.net.URL;
import java.net.URLConnection;
import java.nio.charset.StandardCharsets;
import java.security.cert.X509Certificate;
import java.util.Map;
import javax.net.ssl.HostnameVerifier;
import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.SSLSession;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;

/**
 * 独立HTTP工具类 - 不依赖任何框架
 * 支持GET、POST、HTTPS请求
 * 
 * <AUTHOR>
 */
public class HttpUtils {
    
    /** UTF-8编码 */
    private static final String UTF8 = "UTF-8";
    
    /** 默认Content-Type */
    private static final String DEFAULT_CONTENT_TYPE = "application/x-www-form-urlencoded";
    
    /** JSON Content-Type */
    public static final String JSON_CONTENT_TYPE = "application/json";
    
    /** 默认User-Agent */
    private static final String DEFAULT_USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
    
    /** 连接超时时间(毫秒) */
    private static final int CONNECT_TIMEOUT = 30000;
    
    /** 读取超时时间(毫秒) */
    private static final int READ_TIMEOUT = 30000;

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @return 响应结果
     */
    public static String sendGet(String url) {
        return sendGet(url);
    }

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @param params 请求参数Map
     * @return 响应结果
     */
    public static String sendGet(String url, Map<String, String> params) {
        return sendGet(url, buildParamString(params));
    }

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @param param 请求参数字符串，格式：name1=value1&name2=value2
     * @return 响应结果
     */
    public static String sendGet(String url, String param) {
        return sendGet(url, param, UTF8);
    }

    /**
     * 发送GET请求
     *
     * @param url 请求URL
     * @param param 请求参数字符串
     * @param charset 字符编码
     * @return 响应结果
     */
    public static String sendGet(String url, String param, String charset) {
        StringBuilder result = new StringBuilder();
        BufferedReader in = null;
        
        try {
            String urlNameString = isNotBlank(param) ? url + "?" + param : url;
            System.out.println("GET请求: " + urlNameString);
            
            URL realUrl = new URL(urlNameString);
            URLConnection connection = realUrl.openConnection();
            
            // 设置请求头
            setDefaultHeaders(connection);
            connection.setConnectTimeout(CONNECT_TIMEOUT);
            connection.setReadTimeout(READ_TIMEOUT);
            
            connection.connect();
            
            in = new BufferedReader(new InputStreamReader(connection.getInputStream(), charset));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            
            System.out.println("GET响应: " + result.toString());
            
        } catch (ConnectException e) {
            System.err.println("GET请求连接异常: " + url + ", 参数: " + param + ", 错误: " + e.getMessage());
        } catch (SocketTimeoutException e) {
            System.err.println("GET请求超时: " + url + ", 参数: " + param + ", 错误: " + e.getMessage());
        } catch (IOException e) {
            System.err.println("GET请求IO异常: " + url + ", 参数: " + param + ", 错误: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("GET请求异常: " + url + ", 参数: " + param + ", 错误: " + e.getMessage());
        } finally {
            closeQuietly(in);
        }
        
        return result.toString();
    }

    /**
     * 发送POST请求
     *
     * @param url 请求URL
     * @param param 请求参数字符串
     * @return 响应结果
     */
    public static String sendPost(String url, String param) {
        return sendPost(url, param, DEFAULT_CONTENT_TYPE);
    }

    /**
     * 发送POST请求
     *
     * @param url 请求URL
     * @param params 请求参数Map
     * @return 响应结果
     */
    public static String sendPost(String url, Map<String, String> params) {
        return sendPost(url, buildParamString(params), DEFAULT_CONTENT_TYPE);
    }

    /**
     * 发送POST JSON请求
     *
     * @param url 请求URL
     * @param jsonParam JSON参数字符串
     * @return 响应结果
     */
    public static String sendPostJson(String url, String jsonParam) {
        return sendPost(url, jsonParam, JSON_CONTENT_TYPE);
    }

    /**
     * 发送POST请求
     * 
     * @param url 请求URL
     * @param param 请求参数
     * @param contentType 内容类型
     * @return 响应结果
     */
    public static String sendPost(String url, String param, String contentType) {
        PrintWriter out = null;
        BufferedReader in = null;
        StringBuilder result = new StringBuilder();
        
        try {
            System.out.println("POST请求: " + url);
            System.out.println("POST参数: " + param);
            
            URL realUrl = new URL(url);
            URLConnection conn = realUrl.openConnection();
            
            // 设置请求头
            setDefaultHeaders(conn);
            conn.setRequestProperty("Content-Type", contentType);
            conn.setConnectTimeout(CONNECT_TIMEOUT);
            conn.setReadTimeout(READ_TIMEOUT);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            
            // 发送参数
            out = new PrintWriter(conn.getOutputStream());
            out.print(param);
            out.flush();
            
            // 读取响应
            in = new BufferedReader(new InputStreamReader(conn.getInputStream(), StandardCharsets.UTF_8));
            String line;
            while ((line = in.readLine()) != null) {
                result.append(line);
            }
            
            System.out.println("POST响应: " + result.toString());
            
        } catch (ConnectException e) {
            System.err.println("POST请求连接异常: " + url + ", 参数: " + param + ", 错误: " + e.getMessage());
        } catch (SocketTimeoutException e) {
            System.err.println("POST请求超时: " + url + ", 参数: " + param + ", 错误: " + e.getMessage());
        } catch (IOException e) {
            System.err.println("POST请求IO异常: " + url + ", 参数: " + param + ", 错误: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("POST请求异常: " + url + ", 参数: " + param + ", 错误: " + e.getMessage());
        } finally {
            closeQuietly(out);
            closeQuietly(in);
        }
        
        return result.toString();
    }

    /**
     * 发送HTTPS POST请求
     *
     * @param url 请求URL
     * @param param 请求参数
     * @return 响应结果
     */
    public static String sendSSLPost(String url, String param) {
        return sendSSLPost(url, param, DEFAULT_CONTENT_TYPE);
    }

    /**
     * 发送HTTPS POST请求
     *
     * @param url 请求URL
     * @param param 请求参数
     * @param contentType 内容类型
     * @return 响应结果
     */
    public static String sendSSLPost(String url, String param, String contentType) {
        StringBuilder result = new StringBuilder();
        BufferedReader br = null;
        
        try {
            System.out.println("HTTPS POST请求: " + url);
            System.out.println("HTTPS POST参数: " + param);
            
            // 创建SSL上下文
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, new TrustManager[]{new TrustAnyTrustManager()}, new java.security.SecureRandom());
            
            URL console = new URL(url);
            HttpsURLConnection conn = (HttpsURLConnection) console.openConnection();
            
            // 设置请求头
            setDefaultHeaders(conn);
            conn.setRequestProperty("Content-Type", contentType);
            conn.setConnectTimeout(CONNECT_TIMEOUT);
            conn.setReadTimeout(READ_TIMEOUT);
            conn.setDoOutput(true);
            conn.setDoInput(true);
            
            // 设置SSL
            conn.setSSLSocketFactory(sc.getSocketFactory());
            conn.setHostnameVerifier(new TrustAnyHostnameVerifier());
            
            // 发送参数
            if (isNotBlank(param)) {
                try (PrintWriter out = new PrintWriter(conn.getOutputStream())) {
                    out.print(param);
                    out.flush();
                }
            }
            
            // 读取响应
            InputStream is = conn.getInputStream();
            br = new BufferedReader(new InputStreamReader(is, StandardCharsets.UTF_8));
            String ret;
            while ((ret = br.readLine()) != null) {
                if (isNotBlank(ret)) {
                    result.append(ret);
                }
            }
            
            System.out.println("HTTPS POST响应: " + result.toString());
            conn.disconnect();
            
        } catch (ConnectException e) {
            System.err.println("HTTPS POST连接异常: " + url + ", 参数: " + param + ", 错误: " + e.getMessage());
        } catch (SocketTimeoutException e) {
            System.err.println("HTTPS POST超时: " + url + ", 参数: " + param + ", 错误: " + e.getMessage());
        } catch (IOException e) {
            System.err.println("HTTPS POST IO异常: " + url + ", 参数: " + param + ", 错误: " + e.getMessage());
        } catch (Exception e) {
            System.err.println("HTTPS POST异常: " + url + ", 参数: " + param + ", 错误: " + e.getMessage());
        } finally {
            closeQuietly(br);
        }
        
        return result.toString();
    }

    /**
     * 设置默认请求头
     */
    private static void setDefaultHeaders(URLConnection connection) {
        connection.setRequestProperty("accept", "*/*");
        connection.setRequestProperty("connection", "Keep-Alive");
        connection.setRequestProperty("user-agent", DEFAULT_USER_AGENT);
        connection.setRequestProperty("Accept-Charset", "utf-8");
    }

    /**
     * 构建参数字符串
     */
    private static String buildParamString(Map<String, String> params) {
        if (params == null || params.isEmpty()) {
            return null;
        }
        
        StringBuilder sb = new StringBuilder();
        for (Map.Entry<String, String> entry : params.entrySet()) {
            if (sb.length() > 0) {
                sb.append("&");
            }
            sb.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return sb.toString();
    }

    /**
     * 判断字符串是否不为空
     */
    private static boolean isNotBlank(String str) {
        return str != null && !str.trim().isEmpty();
    }

    /**
     * 安静关闭资源
     */
    private static void closeQuietly(AutoCloseable closeable) {
        if (closeable != null) {
            try {
                closeable.close();
            } catch (Exception e) {
                // 忽略关闭异常
            }
        }
    }

    /**
     * 信任所有证书的TrustManager
     */
    private static class TrustAnyTrustManager implements X509TrustManager {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) {
        }

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) {
        }

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[]{};
        }
    }

    /**
     * 信任所有主机名的HostnameVerifier
     */
    private static class TrustAnyHostnameVerifier implements HostnameVerifier {
        @Override
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    }
}

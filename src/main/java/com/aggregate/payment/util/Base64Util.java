package com.aggregate.payment.util;

import lombok.extern.slf4j.Slf4j;
import java.nio.charset.StandardCharsets;
import java.util.Base64;

/**
 * Base64编码解码工具类
 * 
 * <AUTHOR>
 */
@Slf4j
public class Base64Util {

    /**
     * 编码字符串为Base64
     * 
     * @param content 待编码内容
     * @return Base64编码后的字符串
     */
    public static String encode(String content) {
        try {
            if (content == null) {
                return null;
            }
            return Base64.getEncoder().encodeToString(content.getBytes(StandardCharsets.UTF_8));
        } catch (Exception e) {
            log.error("Base64编码失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解码Base64字符串
     * 
     * @param base64Content Base64编码的字符串
     * @return 解码后的原始字符串
     */
    public static String decode(String base64Content) {
        try {
            if (base64Content == null) {
                return null;
            }
            byte[] decodedBytes = Base64.getDecoder().decode(base64Content);
            return new String(decodedBytes, StandardCharsets.UTF_8);
        } catch (Exception e) {
            log.error("Base64解码失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 编码字节数组为Base64
     * 
     * @param bytes 待编码的字节数组
     * @return Base64编码后的字符串
     */
    public static String encodeBytes(byte[] bytes) {
        try {
            if (bytes == null) {
                return null;
            }
            return Base64.getEncoder().encodeToString(bytes);
        } catch (Exception e) {
            log.error("Base64编码字节数组失败: {}", e.getMessage(), e);
            return null;
        }
    }

    /**
     * 解码Base64字符串为字节数组
     * 
     * @param base64Content Base64编码的字符串
     * @return 解码后的字节数组
     */
    public static byte[] decodeToBytes(String base64Content) {
        try {
            if (base64Content == null) {
                return null;
            }
            return Base64.getDecoder().decode(base64Content);
        } catch (Exception e) {
            log.error("Base64解码为字节数组失败: {}", e.getMessage(), e);
            return null;
        }
    }
}
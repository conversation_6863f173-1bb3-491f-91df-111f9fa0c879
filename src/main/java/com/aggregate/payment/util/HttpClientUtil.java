package com.aggregate.payment.util;

import com.alibaba.fastjson2.JSON;
import lombok.extern.slf4j.Slf4j;
import org.springframework.util.StringUtils;

import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;
import javax.net.ssl.*;
import java.security.cert.X509Certificate;

/**
 * HTTP请求工具类 - 支持多种请求方式和参数类型
 * 
 * <AUTHOR>
 */
@Slf4j
public class HttpClientUtil {
    
    /** 默认连接超时时间(毫秒) */
    private static final int DEFAULT_CONNECT_TIMEOUT = 30000;
    
    /** 默认读取超时时间(毫秒) */
    private static final int DEFAULT_READ_TIMEOUT = 30000;
    
    /** 默认字符编码 */
    private static final String DEFAULT_CHARSET = "UTF-8";
    
    /** 默认User-Agent */
    private static final String DEFAULT_USER_AGENT = "HttpClientUtil/1.0";

    /**
     * HTTP请求构建器
     */
    public static class HttpRequestBuilder {
        private String url;
        private String method = "GET";
        private Map<String, String> headers = new HashMap<>();
        private Map<String, String> params = new HashMap<>();
        private String rawBody;
        private String contentType;
        private int connectTimeout = DEFAULT_CONNECT_TIMEOUT;
        private int readTimeout = DEFAULT_READ_TIMEOUT;
        private String charset = DEFAULT_CHARSET;
        private boolean followRedirects = true;
        private boolean trustAllCerts = false;

        public HttpRequestBuilder url(String url) {
            this.url = url;
            return this;
        }

        public HttpRequestBuilder get() {
            this.method = "GET";
            return this;
        }

        public HttpRequestBuilder post() {
            this.method = "POST";
            return this;
        }

        public HttpRequestBuilder put() {
            this.method = "PUT";
            return this;
        }

        public HttpRequestBuilder delete() {
            this.method = "DELETE";
            return this;
        }

        public HttpRequestBuilder method(String method) {
            this.method = method;
            return this;
        }

        public HttpRequestBuilder header(String name, String value) {
            this.headers.put(name, value);
            return this;
        }

        public HttpRequestBuilder headers(Map<String, String> headers) {
            if (headers != null) {
                this.headers.putAll(headers);
            }
            return this;
        }

        public HttpRequestBuilder param(String name, String value) {
            this.params.put(name, value);
            return this;
        }

        public HttpRequestBuilder params(Map<String, String> params) {
            if (params != null) {
                this.params.putAll(params);
            }
            return this;
        }

        public HttpRequestBuilder rawBody(String rawBody) {
            this.rawBody = rawBody;
            return this;
        }

        public HttpRequestBuilder jsonBody(Object obj) {
            this.rawBody = JSON.toJSONString(obj);
            this.contentType = "application/json";
            return this;
        }

        public HttpRequestBuilder formBody(Map<String, String> formData) {
            this.rawBody = buildFormData(formData);
            this.contentType = "application/x-www-form-urlencoded";
            return this;
        }

        public HttpRequestBuilder contentType(String contentType) {
            this.contentType = contentType;
            return this;
        }

        public HttpRequestBuilder connectTimeout(int timeout) {
            this.connectTimeout = timeout;
            return this;
        }

        public HttpRequestBuilder readTimeout(int timeout) {
            this.readTimeout = timeout;
            return this;
        }

        public HttpRequestBuilder charset(String charset) {
            this.charset = charset;
            return this;
        }

        public HttpRequestBuilder followRedirects(boolean follow) {
            this.followRedirects = follow;
            return this;
        }

        public HttpRequestBuilder trustAllCerts(boolean trust) {
            this.trustAllCerts = trust;
            return this;
        }

        /**
         * 执行请求并返回响应
         */
        public HttpResponse execute() {
            return HttpClientUtil.executeRequest(this);
        }

        /**
         * 执行请求并返回字符串结果
         */
        public String executeAsString() {
            HttpResponse response = execute();
            return response.getBody();
        }

        /**
         * 执行请求并返回JSON对象
         */
        public <T> T executeAsJson(Class<T> clazz) {
            String responseBody = executeAsString();
            if (StringUtils.hasText(responseBody)) {
                return JSON.parseObject(responseBody, clazz);
            }
            return null;
        }

        private String buildFormData(Map<String, String> formData) {
            if (formData == null || formData.isEmpty()) {
                return "";
            }
            
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> entry : formData.entrySet()) {
                if (sb.length() > 0) {
                    sb.append("&");
                }
                try {
                    sb.append(URLEncoder.encode(entry.getKey(), charset))
                      .append("=")
                      .append(URLEncoder.encode(entry.getValue(), charset));
                } catch (UnsupportedEncodingException e) {
                    log.error("编码参数失败: {}", e.getMessage());
                }
            }
            return sb.toString();
        }
    }

    /**
     * HTTP响应对象
     */
    public static class HttpResponse {
        private int statusCode;
        private String statusMessage;
        private Map<String, String> headers = new HashMap<>();
        private String body;
        private long responseTime;

        public HttpResponse(int statusCode, String statusMessage) {
            this.statusCode = statusCode;
            this.statusMessage = statusMessage;
        }

        // Getters and Setters
        public int getStatusCode() { return statusCode; }
        public void setStatusCode(int statusCode) { this.statusCode = statusCode; }
        
        public String getStatusMessage() { return statusMessage; }
        public void setStatusMessage(String statusMessage) { this.statusMessage = statusMessage; }
        
        public Map<String, String> getHeaders() { return headers; }
        public void setHeaders(Map<String, String> headers) { this.headers = headers; }
        
        public String getBody() { return body; }
        public void setBody(String body) { this.body = body; }
        
        public long getResponseTime() { return responseTime; }
        public void setResponseTime(long responseTime) { this.responseTime = responseTime; }

        public boolean isSuccess() {
            return statusCode >= 200 && statusCode < 300;
        }

        public String getHeader(String name) {
            return headers.get(name);
        }

        public void addHeader(String name, String value) {
            headers.put(name, value);
        }

        @Override
        public String toString() {
            return String.format("HttpResponse{statusCode=%d, statusMessage='%s', responseTime=%dms, bodyLength=%d}", 
                    statusCode, statusMessage, responseTime, body != null ? body.length() : 0);
        }
    }

    /**
     * 创建HTTP请求构建器
     */
    public static HttpRequestBuilder create() {
        return new HttpRequestBuilder();
    }

    /**
     * 创建GET请求构建器
     */
    public static HttpRequestBuilder get(String url) {
        return new HttpRequestBuilder().url(url).get();
    }

    /**
     * 创建POST请求构建器
     */
    public static HttpRequestBuilder post(String url) {
        return new HttpRequestBuilder().url(url).post();
    }

    /**
     * 创建PUT请求构建器
     */
    public static HttpRequestBuilder put(String url) {
        return new HttpRequestBuilder().url(url).put();
    }

    /**
     * 创建DELETE请求构建器
     */
    public static HttpRequestBuilder delete(String url) {
        return new HttpRequestBuilder().url(url).delete();
    }

    /**
     * 执行HTTP请求
     */
    private static HttpResponse executeRequest(HttpRequestBuilder builder) {
        long startTime = System.currentTimeMillis();
        HttpURLConnection connection = null;
        
        try {
            // 构建完整URL
            String fullUrl = buildFullUrl(builder.url, builder.params, "GET".equals(builder.method));
            log.debug("执行HTTP请求: {} {}", builder.method, fullUrl);
            
            URL url = new URL(fullUrl);
            connection = (HttpURLConnection) url.openConnection();
            
            // 配置HTTPS
            if (connection instanceof HttpsURLConnection && builder.trustAllCerts) {
                configureHttps((HttpsURLConnection) connection);
            }
            
            // 设置请求方法
            connection.setRequestMethod(builder.method);
            
            // 设置超时
            connection.setConnectTimeout(builder.connectTimeout);
            connection.setReadTimeout(builder.readTimeout);
            
            // 设置是否跟随重定向
            connection.setInstanceFollowRedirects(builder.followRedirects);
            
            // 设置默认请求头
            connection.setRequestProperty("User-Agent", DEFAULT_USER_AGENT);
            connection.setRequestProperty("Accept", "*/*");
            connection.setRequestProperty("Connection", "keep-alive");
            connection.setRequestProperty("Accept-Charset", builder.charset);
            
            // 设置Content-Type
            if (StringUtils.hasText(builder.contentType)) {
                connection.setRequestProperty("Content-Type", builder.contentType + "; charset=" + builder.charset);
            }
            
            // 设置自定义请求头
            for (Map.Entry<String, String> header : builder.headers.entrySet()) {
                connection.setRequestProperty(header.getKey(), header.getValue());
            }
            
            // 发送请求体
            if (StringUtils.hasText(builder.rawBody) && !"GET".equals(builder.method)) {
                connection.setDoOutput(true);
                try (PrintWriter writer = new PrintWriter(new OutputStreamWriter(
                        connection.getOutputStream(), builder.charset))) {
                    writer.print(builder.rawBody);
                    writer.flush();
                }
                log.debug("发送请求体: {}", builder.rawBody);
            }
            
            // 获取响应
            int statusCode = connection.getResponseCode();
            String statusMessage = connection.getResponseMessage();
            
            HttpResponse response = new HttpResponse(statusCode, statusMessage);
            
            // 读取响应头
            for (Map.Entry<String, java.util.List<String>> header : connection.getHeaderFields().entrySet()) {
                if (header.getKey() != null && !header.getValue().isEmpty()) {
                    response.addHeader(header.getKey(), header.getValue().get(0));
                }
            }
            
            // 读取响应体
            InputStream inputStream = null;
            try {
                inputStream = statusCode >= 400 ? connection.getErrorStream() : connection.getInputStream();
                if (inputStream != null) {
                    String responseBody = readInputStream(inputStream, builder.charset);
                    response.setBody(responseBody);
                }
            } catch (IOException e) {
                log.warn("读取响应体失败: {}", e.getMessage());
            }
            
            long responseTime = System.currentTimeMillis() - startTime;
            response.setResponseTime(responseTime);
            
            log.debug("HTTP请求完成: {}", response);
            return response;
            
        } catch (ConnectException e) {
            log.error("HTTP连接异常: {}", e.getMessage());
            throw new RuntimeException("连接失败: " + e.getMessage(), e);
        } catch (SocketTimeoutException e) {
            log.error("HTTP请求超时: {}", e.getMessage());
            throw new RuntimeException("请求超时: " + e.getMessage(), e);
        } catch (IOException e) {
            log.error("HTTP请求IO异常: {}", e.getMessage());
            throw new RuntimeException("请求失败: " + e.getMessage(), e);
        } catch (Exception e) {
            log.error("HTTP请求异常: {}", e.getMessage(), e);
            throw new RuntimeException("请求异常: " + e.getMessage(), e);
        } finally {
            if (connection != null) {
                connection.disconnect();
            }
        }
    }

    /**
     * 构建完整URL
     */
    private static String buildFullUrl(String baseUrl, Map<String, String> params, boolean appendParams) {
        if (!appendParams || params.isEmpty()) {
            return baseUrl;
        }
        
        StringBuilder url = new StringBuilder(baseUrl);
        if (!baseUrl.contains("?")) {
            url.append("?");
        } else {
            url.append("&");
        }
        
        boolean first = true;
        for (Map.Entry<String, String> param : params.entrySet()) {
            if (!first) {
                url.append("&");
            }
            try {
                url.append(URLEncoder.encode(param.getKey(), DEFAULT_CHARSET))
                   .append("=")
                   .append(URLEncoder.encode(param.getValue(), DEFAULT_CHARSET));
            } catch (UnsupportedEncodingException e) {
                log.error("编码URL参数失败: {}", e.getMessage());
            }
            first = false;
        }
        
        return url.toString();
    }

    /**
     * 读取输入流
     */
    private static String readInputStream(InputStream inputStream, String charset) throws IOException {
        StringBuilder result = new StringBuilder();
        try (BufferedReader reader = new BufferedReader(new InputStreamReader(inputStream, charset))) {
            String line;
            while ((line = reader.readLine()) != null) {
                result.append(line);
            }
        }
        return result.toString();
    }

    /**
     * 配置HTTPS连接，信任所有证书
     */
    private static void configureHttps(HttpsURLConnection connection) {
        try {
            SSLContext sc = SSLContext.getInstance("SSL");
            sc.init(null, new TrustManager[]{new TrustAllTrustManager()}, new java.security.SecureRandom());
            connection.setSSLSocketFactory(sc.getSocketFactory());
            connection.setHostnameVerifier(new TrustAllHostnameVerifier());
        } catch (Exception e) {
            log.error("配置HTTPS失败: {}", e.getMessage());
        }
    }

    /**
     * 信任所有证书的TrustManager
     */
    private static class TrustAllTrustManager implements X509TrustManager {
        @Override
        public void checkClientTrusted(X509Certificate[] chain, String authType) {}

        @Override
        public void checkServerTrusted(X509Certificate[] chain, String authType) {}

        @Override
        public X509Certificate[] getAcceptedIssuers() {
            return new X509Certificate[]{};
        }
    }

    /**
     * 信任所有主机名的HostnameVerifier
     */
    private static class TrustAllHostnameVerifier implements HostnameVerifier {
        @Override
        public boolean verify(String hostname, SSLSession session) {
            return true;
        }
    }
}
package com.aggregate.payment.util;

import cn.jpush.api.JPushClient;
import cn.jpush.api.push.model.Message;
import cn.jpush.api.push.model.Options;
import cn.jpush.api.push.model.Platform;
import cn.jpush.api.push.model.audience.Audience;
import cn.jpush.api.push.model.notification.AndroidNotification;
import cn.jpush.api.push.model.notification.IosNotification;
import cn.jpush.api.push.model.notification.Notification;
import cn.jpush.api.push.model.notification.PlatformNotification;

public class JPushTransmissionExample {

    // 替换为你的 appKey 和 masterSecret
    private static final String APP_KEY = "你的AppKey";
    private static final String MASTER_SECRET = "你的MasterSecret";

    public static void main(String[] args) {
        JPushClient jPushClient = new JPushClient(MASTER_SECRET, APP_KEY);

        // 构建透传消息内容（可以是 JSON 字符串）
        String transmissionContent = "{\"key1\":\"value1\", \"key2\":\"value2\"}";

        Message message = Message.newBuilder()
                .setMsgContent(transmissionContent)
                .setTitle("透传消息标题")  // 可选
                .addExtra("from", "极光推送") // 可选附加字段
                .build();
        Notification notification = Notification.newBuilder()
                .setAlert("到账300元") // 通知内容
                .addPlatformNotification(AndroidNotification.newBuilder()
                        .setAlert("到账300元")
                        .setTitle("语音提醒")
                        .addExtra("voice", "true") // 可自定义字段，App中识别后触发语音播报
                        .build())
                .addPlatformNotification(IosNotification.newBuilder()
                        .setAlert("到账300元")
                        .setSound("default") // iOS 播放声音
                        .addExtra("voice", "true")
                        .build())
                .build();

        // 设置推送平台（Android/iOS/All）
        Platform platform = Platform.all();

        // 设置推送目标（例如：所有设备）
        Audience audience = Audience.alias();

        // 设置推送选项（如离线保存时间、是否唤醒 App 等）
        Options options = Options.newBuilder()
                .setTimeToLive(86400) // 离线保存时间，单位秒（默认 24 小时）
                .setApnsProduction(false) // iOS 是否为生产环境
                .build();

        try {
            cn.jpush.api.push.model.PushPayload payload = cn.jpush.api.push.model.PushPayload.newBuilder()
                    .setPlatform(platform)
                    .setAudience(audience)
                    .setMessage(message)
                    .setOptions(options)
                    .build();

            // 发送推送
            jPushClient.sendPush(payload);
            System.out.println("推送发送成功！");
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("推送发送失败：" + e.getMessage());
        }
    }
}
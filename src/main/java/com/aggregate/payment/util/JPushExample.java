package com.aggregate.payment.util;

import com.alibaba.fastjson2.JSONObject;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;

import java.util.Base64;

public class JPushExample {

    // 替换为你的极光推送 AppKey 和 Master Secret
    private static final String APP_KEY = "7af319024076a5414fce0cb1";
    private static final String MASTER_SECRET = "f99c7264ea3cdcde93150b19";

    public static void main(String[] args) {
        try {
            // 1. 构建请求URL
            String url = "https://api.jpush.cn/v3/push";

            // 2. 创建HttpClient
            CloseableHttpClient client = HttpClients.createDefault();

            // 3. 创建POST请求
            HttpPost post = new HttpPost(url);

            // 4. 设置请求头 Authorization
            String auth = APP_KEY + ":" + MASTER_SECRET;
            String encodedAuth = Base64.getEncoder().encodeToString(auth.getBytes());
            post.setHeader("Authorization", "Basic " + encodedAuth);
            post.setHeader("Content-Type", "application/json");

            // 5. 构建请求体 JSON
            JSONObject body = new JSONObject();
            body.put("platform", "all");

            // audience 设置为标签
            JSONObject audience = new JSONObject();
            audience.put("alias", new String[]{"merid_1"});
            body.put("audience", audience);

            // notification 设置
            JSONObject notification = new JSONObject();
            notification.put("alert", "Hello, JPush!");

            // Android 通知
            JSONObject android = new JSONObject();
            android.put("alert", "您有一条线下订单成功支付：{}元");
            android.put("title", "支付通知");
            android.put("builder_id", 1);
            android.put("extras", new JSONObject().put("key", "value"));
            android.put("sound","default");

            // iOS 通知
            JSONObject ios = new JSONObject();
            ios.put("alert", "Hi, iOS!");
            ios.put("sound", "default");
            ios.put("badge", "+1");
            ios.put("extras", new JSONObject().put("key", "value"));

            notification.put("android", android);
            notification.put("ios", ios);
            body.put("notification", notification);

            // options 设置
            JSONObject options = new JSONObject();
            options.put("time_to_live", 60);
            options.put("apns_production", false);
            body.put("options", options);

            // 6. 设置请求体
            StringEntity entity = new StringEntity(body.toString(), "UTF-8");
            entity.setContentType("application/json");
            post.setEntity(entity);

            // 7. 发送请求
            HttpResponse response = client.execute(post);
            System.out.println(body);

            // 8. 获取响应
            String responseString = EntityUtils.toString(response.getEntity(), "UTF-8");
            System.out.println("响应结果：\n" + responseString);

        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}
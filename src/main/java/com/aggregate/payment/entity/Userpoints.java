package com.aggregate.payment.entity;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class Userpoints {
    public String userType;
    private Integer uid;
    private Integer spreadUid;
    private Float points;
    private String account;
    private String pwd;
    private String lastIp;
    private String nickname;
    private String phone;
    private String avatar;
    private String qr_code_key;
    private Integer serviceProviderType;
    private Integer serviceProviderId;
    private String serviceProviderName;
    private BigDecimal pointsGetCount;
}

package com.aggregate.payment.entity;

import cn.hutool.core.date.DateTime;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 线下订单实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("ml_offline_order")
public class OfflineOrder extends BaseEntity {

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 商户ID */
    private Long merId;
    //流水号
    private String orderCode;

    /** 分佣比例 */
    private BigDecimal commissionRate;

    /** 购买者openid */
    private String buyerOpenid;

    /** 账户 */
    private String account;
    private Integer uid;
    /** 商品名字 */
    private String productName;

    /** 商品描述 */
    private String productDescription;

    /** 支付金额 */
    private BigDecimal paymentAmount;

    /** 支付状态(0未1完) */
    private Integer payState;

    /** 订单状态(0未1完2错) */
    private Integer orderState;

    /** 商户分佣 */
    private BigDecimal commissionMer;

    /** 平台分佣 */
    private BigDecimal commissionPl;

    /** 创建时间 */
    private LocalDateTime createTime;


    /** 更新时间 */
    private LocalDateTime updateTime;
    private Integer spreadUid;
    private String ip;
    private String mac;
    private String wsId;
    private String bankUserCode;
    private String platBankCode;
    private Integer serviceProviderType;
    private Integer serviceProviderId;
    private String serviceProviderName;
    private String mrchSno;
    private String merName;
    private String payType;
    private LocalDateTime startTime;
    private LocalDateTime endTime;


}
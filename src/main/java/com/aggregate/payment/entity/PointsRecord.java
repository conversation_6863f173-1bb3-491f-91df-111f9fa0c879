package com.aggregate.payment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 积分明细记录实体类
 *
 * <AUTHOR>
 */
@Data
@TableName("ml_points_record")
public class PointsRecord extends BaseEntity {

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 说明 */
    private String description;

    /** 积分 */
    private Float points;

    /** 订单编号 */
    private String orderNo;

    /** 类型(0=扣除, 1=新增) */
    private Integer type;

    /** 用户ID */
    private String userId;

    /** 状态(0=未结算, 1=已结算) */
    private Integer status;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 更新时间 */
    private LocalDateTime updateTime;
    /** 用户类型(0-C端消费者,1-B端商户) */
    private Integer userType;
    private String totalPeriods;
    private Integer dateChar;
    private Integer invitorTag;
    private Integer orderType;
}

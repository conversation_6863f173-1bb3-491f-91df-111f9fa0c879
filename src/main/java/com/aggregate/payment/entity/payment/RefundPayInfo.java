package com.aggregate.payment.entity.payment;

import lombok.Data;

import java.util.List;

@Data
public class RefundPayInfo {
    private String mechNo;
    private String oriThirdSeqNo;
    private String refundAmt;
    private String channelCode;
    private String ip;
    private String mac;
    private String mrchAcctNo;
    private String mrchSno;
    private String remark;
    private String wsId;
    private String txTime;
    private String txSno;
    private List<MatchInfoDto>  elmtMatchInfoArray;
}

package com.aggregate.payment.entity.payment;


import lombok.Data;

import java.util.ArrayList;

/**
 * 支付下单
 * <AUTHOR>
 * @date 2024/2/6 13:57
 */
@Data
public class PayDto {


    /** 商户号 */
    private String mechNo;

    /** 金额 */
    private String tranAmt;

    /** 币种 */
    private String ccy = "156";

    /** 支付类型 THS_ALI-支付宝；THS_WX-微信； */
    private String payType;

    /** 商品标题 */
    private String title;

    /** 商品描述 */
    private String dsc;

    /** 支付标志 是否需确认收货标志： 1-是 0-否 */
    private String paymentFlag;

    /** 备注 */
    private String remark;

    /** 终端标识 */
    private String wsId;

    /** ip地址 */
    private String ip;
    private String mac;
    private String txSno;
    private String txTime;
    private String mrchSno;

    /** 支付指令信息数组 */
    private ArrayList<PayInfoDto> pyInsrInfoArray;

    /** 要素匹配信息数组 */
    private ArrayList<MatchInfoDto> elmtMatchInfoArray;


}

package com.aggregate.payment.entity.payment;

import com.aggregate.payment.entity.payment.MatchInfoDto;
import lombok.Data;

import java.util.ArrayList;

/**
 * ClassName: PayConfirmedDto
 * Description: 支付确认 信息类
 * Author: WRM
 * DateTime: 2025/7/14 00:02
 * Version: 1.0
 */
@Data
public class PayConfirmedDto{
    /** 平台编号 */
    private String mrchAcctNo;

    /** 商户号 */
    private String mechNo;

    /** 交易流水号 */
    private String oriThirdSeqNo;

    /** 交易金额 */
    private String tranAmt;

    /** 操作类型 */
    private String operationType;

    /** 要素匹配信息数组 */
    private ArrayList<MatchInfoDto> elmtMatchInfoArray;
}

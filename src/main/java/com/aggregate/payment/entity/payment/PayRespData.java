package com.aggregate.payment.entity.payment;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;

import java.math.BigDecimal;

@Data
public class PayRespData {
    private String mechNo;
    private String ptSeqNo;
    private String acceptNo;
    private String ccy;
    private BigDecimal tranAmt;
    private String cmmssn;
    private String mrchAcctNo;
    private String quickRspString;
    /** 微信公众号ID */
    private String appId;

    /** 时间戳(秒) */
    private String timestamp;

    /** 随机字符串 */
    private String nonceStr;

    /** 预支付交易会话标识 prepay_id=xxx */
    private String packageValue;


    /** 微信支付签名 */
    private String paySign;
    private String signCert;
    private String remark;

}

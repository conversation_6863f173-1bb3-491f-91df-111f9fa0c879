package com.aggregate.payment.entity.payment;

import lombok.Data;
import java.util.ArrayList;

/**
 * ClassName: PayConfirmDto
 * Description: 支付确认请求实体类
 * Author: WRM
 * DateTime: 2025/1/14 
 * Version: 1.0
 */
@Data
public class PayConfirmDto {
    /** 要素匹配信息数组 */
    private ArrayList<MatchInfoDto> elmtMatchInfoArray;
    
    /** IP地址 */
    private String ip;
    
    /** MAC地址 */
    private String mac;
    
    /** 商户号 */
    private String mechNo;
    
    /** 商户账户号 */
    private String mrchAcctNo;
    
    /** 商户流水号 */
    private String mrchSno;
    
    /** 操作类型 */
    private String operationType;
    
    /** 原第三方交易流水号 */
    private String oriThirdSeqNo;
    
    /** 交易金额 */
    private String tranAmt;
    
    /** 交易流水号 */
    private String txSno;
    
    /** 交易时间 */
    private String txTime;
    
    /** 终端标识 */
    private String wsId;
}
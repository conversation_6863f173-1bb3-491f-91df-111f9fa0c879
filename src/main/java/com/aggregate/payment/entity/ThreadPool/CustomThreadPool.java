package com.aggregate.payment.entity.ThreadPool;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import java.util.List;
import java.util.concurrent.*;

/**
 * 自定义线程池，支持任务完成后自动销毁
 */
public class CustomThreadPool {
    private static final Logger logger = LoggerFactory.getLogger(CustomThreadPool.class);
    private final ExecutorService executor;
    private final String poolId; // 线程池唯一标识，便于调试

    /**
     * 构造线程池
     * @param corePoolSize 核心线程数
     * @param maxPoolSize 最大线程数
     * @param queueCapacity 任务队列容量
     * @param keepAliveSeconds 空闲线程存活时间（秒）
     */
    public CustomThreadPool(int corePoolSize, int maxPoolSize, int queueCapacity, long keepAliveSeconds) {
        this.poolId = "CustomPool-" + System.currentTimeMillis(); // 用时间戳作为唯一标识
        // 自定义线程工厂，为线程命名（包含池ID）
        ThreadFactory threadFactory = r -> {
            Thread thread = new Thread(r);
            thread.setName(poolId + "-Thread-" + thread.getId()); // 线程名：池ID-线程ID
            thread.setDaemon(false); // 非守护线程，避免主线程退出导致任务中断
            return thread;
        };

        // 初始化线程池
        this.executor = new ThreadPoolExecutor(
                corePoolSize,
                maxPoolSize,
                keepAliveSeconds, TimeUnit.SECONDS,
                new LinkedBlockingQueue<>(queueCapacity), // 有界队列，避免OOM
                threadFactory,
                new ThreadPoolExecutor.AbortPolicy() // 队列满时拒绝任务并抛异常
        );
        logger.info("线程池 [{}] 创建成功，核心线程数：{}，最大线程数：{}", poolId, corePoolSize, maxPoolSize);
    }

    /**
     * 提交任务并返回Future
     */
    public Future<?> submitTask(Runnable task) {
        if (executor.isShutdown()) {
            throw new IllegalStateException("线程池 [" + poolId + "] 已关闭，无法提交任务");
        }
        try {
            return executor.submit(task);
        } catch (RejectedExecutionException e) {
            logger.error("线程池 [" + poolId + "] 任务提交失败（队列满或已关闭）", e);
            throw e; // 向上层抛出，由调用方处理
        }
    }

    /**
     * 优雅关闭线程池，确保任务完成后销毁
     */
    public void shutdownAndAwaitTermination() {
        logger.info("开始销毁线程池 [{}]", poolId);
        if (executor.isTerminated()) {
            logger.info("线程池 [{}] 已销毁，无需重复操作", poolId);
            return;
        }

        // 第一步：拒绝新任务，等待已提交任务完成
        executor.shutdown();
        try {
            // 等待1小时让任务执行完毕（超时则强制关闭）
            if (!executor.awaitTermination(1, TimeUnit.HOURS)) {
                logger.warn("线程池 [{}] 等待1小时未完成，开始强制关闭", poolId);
                // 第二步：强制中断正在执行的任务
                List<Runnable> remainingTasks = executor.shutdownNow();
                logger.warn("线程池 [{}] 强制关闭后，未执行的任务数：{}", poolId, remainingTasks.size());

                // 等待强制关闭完成（最多10分钟）
                if (!executor.awaitTermination(10, TimeUnit.MINUTES)) {
                    logger.error("线程池 [{}] 强制关闭失败，可能存在资源泄漏", poolId);
                }
            }
            logger.info("线程池 [{}] 已彻底销毁", poolId);
        } catch (InterruptedException e) {
            logger.error("线程池 [{}] 销毁被中断", poolId, e);
            // 中断时强制关闭
            executor.shutdownNow();
            Thread.currentThread().interrupt(); // 保留中断状态
        }
    }

    /**
     * 获取线程池ID（用于调试）
     */
    public String getPoolId() {
        return poolId;
    }
}
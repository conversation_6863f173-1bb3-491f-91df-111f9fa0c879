package com.aggregate.payment.entity.pointsSearch;

import lombok.Data;

@Data
public class PointsRecordQuery {
    private Integer pageNum;     // 当前页码
    private Integer pageSize;    // 每页条数
    // 可选查询条件（如果需要）
    private Integer userId;
    private Integer userType;
    private Integer orderType;
    private String orderNo;
    private String beginTime;
    private String endTime;

    public int getOffset() {
        return (pageNum - 1) * pageSize;
    }
}
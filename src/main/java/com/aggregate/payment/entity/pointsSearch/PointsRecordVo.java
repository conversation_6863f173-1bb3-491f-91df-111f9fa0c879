package com.aggregate.payment.entity.pointsSearch;

import lombok.Data;

import java.math.BigDecimal;

@Data
public class PointsRecordVo {
    private Float points;
    private String orderNo;
    private Integer type;
    private Integer userId;
    private Integer userType;
    private Integer orderType;
    private Integer invitorTag;
    private String createTime;
    private String description;

    private BigDecimal orderAmount;
    private String orderAccount;
    private Long merId;
    private String merName;
    private BigDecimal commissionRate;
    private String userAccount;
}
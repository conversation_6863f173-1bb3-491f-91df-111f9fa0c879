package com.aggregate.payment.entity;


import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户积分实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("ml_offline_user")
public class OfflineUser {

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /** 用户openId */
    private String openId;

    /** 手机号码 */
    private String phone;




    private LocalDateTime createTime;
    private LocalDateTime updateTime;
}

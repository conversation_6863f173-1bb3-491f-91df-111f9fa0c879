package com.aggregate.payment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 系统配置参数实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("t_sys_config")
public class SysConfig {
    
    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Integer id;
    
    /** 参数编码 */
    private String code;
    
    /** 参数值 */
    private String value;
    
    /** 参数描述 */
    private String desc;
}
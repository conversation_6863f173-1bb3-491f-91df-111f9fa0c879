package com.aggregate.payment.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

/**
 * 聚合二维码实体类
 * 
 * <AUTHOR>
 */
@Data
@TableName("ml_aggregate_qr_code")
public class AggregateQrCode {

    /** 二维码ID */
    @TableId(type = IdType.AUTO)
    private Long qrCodeId;

    /** 二维码唯一标识 */
    private String qrCodeKey;

    /** 商户ID */
    private Long merchantId;

    /** 商户编号 */
    private String merchantCode;

    /** 二维码类型 (1固定金额 2动态金额) */
    private String qrCodeType;

    /** 固定金额 */
    private BigDecimal fixedAmount;

    /** 服务费率 */
    private BigDecimal serviceFeeRate;

    /** 二维码URL */
    private String qrCodeUrl;

    /** 二维码图片路径 */
    private String qrCodeImagePath;

    /** 二维码状态 (0正常 1停用) */
    private String status;

    /** 过期时间 */
    private LocalDateTime expireTime;

    /** 扫码次数 */
    private Integer scanCount;

    /** 最后扫码时间 */
    private LocalDateTime lastScanTime;

    /** 删除标志 (0代表存在 1代表删除) */
    private String delFlag;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 更新时间 */
    private LocalDateTime updateTime;

    /** 创建者 */
    private String createBy;

    /** 更新者 */
    private String updateBy;

    /** 备注 */
    private String remark;

}

package com.aggregate.payment.scheduleTask;

import com.aggregate.payment.entity.OfflineOrder;
import com.aggregate.payment.entity.payment.MatchInfoDto;
import com.aggregate.payment.entity.payment.PayConfirmDto;
import com.aggregate.payment.entity.payment.ResponseData;
import com.aggregate.payment.mapper.MerchantpointsMapper;
import com.aggregate.payment.mapper.OfflineOrderMapper;
import com.aggregate.payment.util.HttpClientUtil;
import com.alibaba.fastjson2.JSON;
import org.quartz.Job;
import org.quartz.JobExecutionContext;
import org.quartz.JobExecutionException;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;

@Component
public class DailyJob implements Job {
    private static final Logger logger = LoggerFactory.getLogger(DailyJob.class);
    @Autowired
    private OfflineOrderMapper offlineOrderMapper;
    @Autowired
    private MerchantpointsMapper merchantpointsMapper;
    @Value("${aggregate.payment.bank-id}")
    private String bankId;

    @Override
    public void execute(JobExecutionContext context) throws JobExecutionException {
        List<OfflineOrder> offlineOrders = offlineOrderMapper.selectOfflineUFOrders();
        logger.info("定时任务执行，查询到未完成订单数: {}", offlineOrders.size());
    }

    private List<PayConfirmDto> buildPayConfirmDto(List<OfflineOrder> offlineOrders) {
        List<PayConfirmDto> payConfirmDtoList = new ArrayList<PayConfirmDto>();
        for (OfflineOrder offlineOrder : offlineOrders) {
            PayConfirmDto payConfirmDto = new PayConfirmDto();
            payConfirmDto.setMechNo(offlineOrder.getMerId().toString());
            payConfirmDto.setOriThirdSeqNo(offlineOrder.getOrderCode());
            payConfirmDto.setTranAmt(String.valueOf(offlineOrder.getPaymentAmount()));
            //设置分发信息
            ArrayList<MatchInfoDto> MatchInfoDtos = new ArrayList<>();
            MatchInfoDto pl = new MatchInfoDto();
            pl.setSaFlg("1");
            pl.setSaAmt(String.valueOf(offlineOrder.getCommissionPl()));
            pl.setSaPsnNm(offlineOrder.getPlatBankCode());
            MatchInfoDtos.add(pl);
            MatchInfoDto mer = new MatchInfoDto();
            mer.setSaFlg("2");
            mer.setSaAmt(String.valueOf(offlineOrder.getCommissionMer()));
            mer.setSaPsnNm(offlineOrder.getBankUserCode());
            MatchInfoDtos.add(mer);
            payConfirmDto.setElmtMatchInfoArray(MatchInfoDtos);
            payConfirmDto.setIp(offlineOrder.getIp());
            payConfirmDto.setMac(offlineOrder.getMac());
            payConfirmDto.setWsId(offlineOrder.getWsId());
            payConfirmDto.setTxTime(String.valueOf(offlineOrder.getCreateTime()));
            payConfirmDto.setTxSno(offlineOrder.getOrderCode());
            payConfirmDtoList.add(payConfirmDto);

        }
        return payConfirmDtoList;
    }

    public ResponseData sendPayConfirmRequest(PayConfirmDto payConfirmDto) {
        // 构建请求URL（带查询参数）
        String baseUrl = "https://pay.fengzhidi.com/pay-service/v1/order/onlinePayOrder/";
        String fullUrl = baseUrl + "?bankId=" + bankId;

        try {
            // 发起POST请求
            String response = HttpClientUtil.post(fullUrl)
                    .jsonBody(payConfirmDto)
                    .connectTimeout(30000)
                    .readTimeout(30000)
                    .executeAsString();

            logger.info("支付确认请求响应: {}", response);

            // 解析响应
            return JSON.parseObject(response, ResponseData.class);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
package com.aggregate.payment.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 更新二维码请求参数
 *
 * <AUTHOR>
 */
@Data
public class QrCodeUpdateRequest {

    /** 商户ID */
    private Long merchantId;

    /** 服务费率 */
    private BigDecimal serviceFeeRate;

    /** 二维码类型：1=固定金额，2=动态金额 */
    private String qrCodeType = "2";

    /** 固定金额（当qrCodeType为1时必填） */
    private BigDecimal fixedAmount;
}

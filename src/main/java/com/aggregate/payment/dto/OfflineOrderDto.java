package com.aggregate.payment.dto;

import com.aggregate.payment.entity.BaseEntity;
import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
@Data
public class OfflineOrderDto extends BaseEntity {

    /** 主键ID */
    private Long id;

    /** 商户ID */
    private Long merId;
    private String orderCode;

    /** 分佣比例 */
    private BigDecimal commissionRate;

    /** 购买者openid */
    private String buyerOpenid;

    /** 账户 */
    private String account;

    /** 商品名字 */
    private String productName;

    /** 商品描述 */
    private String productDescription;

    /** 支付金额 */
    private BigDecimal paymentAmount;

    /** 支付状态(0未1完) */
    private Integer payState;

    /** 订单状态(0未1完) */
    private Integer orderState;

    /** 商户分佣 */
    private BigDecimal commissionMer;

    /** 平台分佣 */
    private BigDecimal commissionPl;

    /** 创建时间 */
    private LocalDateTime createTime;

    /** 更新时间 */
    private LocalDateTime updateTime;
    /** 终端标识 */
    private String wsId;

    /** ip地址 */
    private String ip;
    private String mac;
    private String payType;
    private String merName;
    private String qrcodeKey;
    private String bankUserCode;
    private String platBankCode;
    private String mrchSno;

}

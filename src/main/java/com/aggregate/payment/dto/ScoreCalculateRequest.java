package com.aggregate.payment.dto;

import lombok.Data;

import java.math.BigDecimal;

/**
 * 积分计算请求参数
 * 
 * <AUTHOR>
 */
@Data
public class ScoreCalculateRequest {

    /** 订单编号 */
    private String orderNo;

    /** 订单实付金额 */
    private BigDecimal orderAmount;

    /** 分佣比例 */
    private BigDecimal commissionRate;

    /** B端商户ID */
    private String merchantId;

    /** C端消费者ID */
    private String consumerId;

    /** C端推荐人ID (可选) */
    private String referrerId;

    /** 积分说明 */
    private String description;
}

package com.aggregate.payment.controller;

import com.aggregate.payment.common.AjaxResult;
import com.aggregate.payment.entity.MlCameraProductRel;
import com.aggregate.payment.service.MlCameraProductRelService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RestController
@RequestMapping("/proCameraRel")
public class MlCameraProductRelController {
    @Autowired
    private MlCameraProductRelService mlCameraProductRelService;
    @PostMapping("/bind")
    public AjaxResult bind(@RequestBody MlCameraProductRel mlCameraProductRel) {
         mlCameraProductRelService.insert(mlCameraProductRel);
        return AjaxResult.success();
    }
    @PostMapping("/unbind")
    public AjaxResult unbind(@RequestBody List<MlCameraProductRel> mlCameraProductRel) {
        mlCameraProductRelService.unBind(mlCameraProductRel);
        return AjaxResult.success();
    }
    @PostMapping("/list")
    public AjaxResult bindProductAndCamera(@RequestBody MlCameraProductRel condition ) {
        List<MlCameraProductRel> mlCameraProductRels = mlCameraProductRelService.selectByCondition(condition);
        return AjaxResult.success(mlCameraProductRels);
    }

}

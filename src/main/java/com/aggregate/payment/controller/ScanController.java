package com.aggregate.payment.controller;
import com.aggregate.payment.service.impl.AggregateQrCodeServiceImpl;
import com.aggregate.payment.service.impl.PaymentAuthServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * 扫码控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
public class ScanController {

    @Resource
    private PaymentAuthServiceImpl paymentAuthService;

    @Resource
    private AggregateQrCodeServiceImpl qrCodeService;

    /**
     * 处理扫码请求
     */
    @GetMapping("/scan")
    public void handleScan(HttpServletRequest request, HttpServletResponse response,
                          @RequestParam("code") String qrCodeKey) throws IOException {
        
        log.info("收到扫码请求: qrCodeKey={}, userAgent={}", 
                qrCodeKey, request.getHeader("User-Agent"));
        
        // 更新扫码统计
        qrCodeService.updateScanCount(qrCodeKey);
        
        // 处理扫码请求
        paymentAuthService.handleScanRequest(request, response, qrCodeKey);
    }
}

package com.aggregate.payment.controller;

import com.aggregate.payment.common.AjaxResult;
import com.aggregate.payment.entity.ThreadPool.ThreadPoolRequest;
import com.aggregate.payment.service.impl.ThreadTaskService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/task")
public class ThreadTaskController {
    @Autowired
    private ThreadTaskService threadTaskService;
    @PostMapping("/execute")
    public AjaxResult executeTask(@RequestBody ThreadPoolRequest threadPoolRequest) {
        return AjaxResult.success(threadTaskService.executeTask(threadPoolRequest));
    }
}

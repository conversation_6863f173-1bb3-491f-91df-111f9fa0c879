package com.aggregate.payment.controller;

import com.aggregate.payment.common.AjaxResult;
import com.aggregate.payment.entity.AggregateQrCode;
import com.aggregate.payment.entity.OfflineUserReInfo;
import com.aggregate.payment.service.IPaymentAuthService;
import com.aggregate.payment.service.impl.AggregateQrCodeServiceImpl;
import com.aggregate.payment.service.impl.OfflineUserServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;

/**
 * 授权回调控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/auth")
public class AuthController {

    @Autowired
    private IPaymentAuthService paymentAuthService;
    @Autowired
    private OfflineUserServiceImpl offlineUserService;
    @Autowired
    private AggregateQrCodeServiceImpl qrCodeService;
    @Value("${aggregate.payment.RedirectUrl}")
    private String RedirectUrl;

    /**
     * 微信授权回调
     */
    @GetMapping("/wechat/callback")
    public void wechatCallback(@RequestParam("code") String code,
                                @RequestParam("state") String qrCodeKey,
                                HttpServletResponse response) throws IOException {
        
        log.info("微信授权回调: code={}, qrCodeKey={}", code, qrCodeKey);
        AggregateQrCode byQrcodeKey = qrCodeService.getByQrcodeKey(qrCodeKey);
        // 获取微信用户openid
        String openid = paymentAuthService.getUserIdFromAuth(code, "WECHAT", qrCodeKey);
        /** 支付类型 THS_ALI-支付宝；THS_WX-微信； */
        String payType = "THS_WX";
        
        if (openid != null) {
            int phoneStatus = paymentAuthService.initUser(openid);
            String merchantName = byQrcodeKey.getMerchantCode();

            String redirectUrl = String.format(
                    RedirectUrl+"?merchantName=%s&openid=%s&qrCodeKey=%s&payType=%s&phoneStatus=%d",
                    URLEncoder.encode(merchantName, "UTF-8"),
                    URLEncoder.encode(openid, "UTF-8"),
                    URLEncoder.encode(qrCodeKey, "UTF-8"),
                    URLEncoder.encode(payType, "UTF-8"),
                    phoneStatus
            );

            response.sendRedirect(redirectUrl);
        }
//        } else {
//            String info = "微信授权失败";
//            String url2 = String.format("http://*************:1554/open.html?path=/pages/store/payment/index&error=%s",
//                    URLEncoder.encode(info, "UTF-8"));
//            response.sendRedirect(url2);
//        }
    }

    /**
     * 支付宝授权回调
     */
    @GetMapping("/alipay/callback")
    public void alipayCallback(@RequestParam(value = "auth_code", required = false) String authCode,
                                @RequestParam(value = "code", required = false) String code,
                                @RequestParam("state") String qrCodeKey,
                                HttpServletRequest request,
                                HttpServletResponse response) throws IOException {

        // 支付宝可能传递 auth_code 或 code，优先使用 auth_code
        String actualAuthCode = authCode != null ? authCode : code;
        String payType = "THS_ALI";
        log.info("支付宝授权回调: authCode={}, code={}, qrCodeKey={}, allParams={}",
                authCode, code, qrCodeKey, request.getParameterMap().keySet());
        // 获取支付宝用户ID
        String userId = paymentAuthService.getUserIdFromAuth(actualAuthCode, "ALIPAY", qrCodeKey);

        if (userId != null) {

            AggregateQrCode byQrcodeKey = qrCodeService.getByQrcodeKey(qrCodeKey);
            int phoneStatus = paymentAuthService.initUser(userId);

            String merchantName = byQrcodeKey.getMerchantCode();

            // 同样的错误 - payType是字符串但使用了%d格式化符
            String redirectUrl = String.format(
                    RedirectUrl+"?merchantName=%s&openid=%s&qrCodeKey=%s&payType=%s&phoneStatus=%d",
                    URLEncoder.encode(merchantName, "UTF-8"),
                    URLEncoder.encode(userId, "UTF-8"),
                    URLEncoder.encode(qrCodeKey, "UTF-8"),
                    URLEncoder.encode(payType, "UTF-8"), // 这里应该用%s而不是%d
                    phoneStatus
            );

            response.sendRedirect(redirectUrl);
        }
//        } else {
//            String info = "支付宝授权失败";
//            String url2 = String.format("http://*************:1554/open.html?path=/pages/store/payment/index&error=%s",
//                    URLEncoder.encode(info, "UTF-8"));
//            response.sendRedirect(url2);
//        }
    }


    /**
     * 手动微信授权（用于浏览器访问）
     */
    @GetMapping("/wechat/manual")
    public void manualWechatAuth(HttpServletResponse response, 
                                @RequestParam("state") String qrCodeKey) throws IOException {
        
        log.info("手动微信授权: qrCodeKey={}", qrCodeKey);
        // 这里可以重定向到微信授权页面，或者显示微信二维码
        response.sendRedirect("/auth/wechat/callback?code=manual_test_code&state=" + qrCodeKey);
    }

    /**
     * 手动支付宝授权（用于浏览器访问）
     */
    @GetMapping("/alipay/manual")
    public void manualAlipayAuth(HttpServletResponse response, 
                                @RequestParam("state") String qrCodeKey) throws IOException {
        
        log.info("手动支付宝授权: qrCodeKey={}", qrCodeKey);
        // 这里可以重定向到支付宝授权页面
        response.sendRedirect("/auth/alipay/callback?auth_code=manual_test_code&state=" + qrCodeKey);
    }
    @PostMapping("/bindPhone")
    public AjaxResult bindPhone(@RequestBody OfflineUserReInfo offlineUser) {
        if (offlineUser == null || offlineUser.getPhone() == null || offlineUser.getSmsCode() == null) {
            return AjaxResult.error("手机号和验证码不能为空");
        }
        return offlineUserService.bindPhoneNumber(offlineUser);
    }
    @PostMapping("/sendSms")
    public AjaxResult sendSms(@RequestBody Map<String, String> req) {
        String phone = req.get("phone");
        if (phone == null || phone.trim().isEmpty()) {
            return AjaxResult.error("手机号不能为空");
        }
        return offlineUserService.getSms(phone);
    }
    @PostMapping("/verifyPhone")
    public AjaxResult verifyPhone(@RequestBody OfflineUserReInfo offlineUser) {
        if (offlineUser == null || offlineUser.getPhone() == null || offlineUser.getSmsCode() == null) {
            return AjaxResult.error("手机号和验证码不能为空");
        }
        return offlineUserService.verifyPhone(offlineUser);
    }
}


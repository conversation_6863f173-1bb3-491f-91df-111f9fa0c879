package com.aggregate.payment.controller;

import com.aggregate.payment.common.AjaxResult;
import com.aggregate.payment.dto.OfflineOrderDto;
import com.aggregate.payment.entity.OfflineOrder;
import com.aggregate.payment.entity.payment.PayRespData;
import com.aggregate.payment.service.IOfflineOrderService;
import com.aggregate.payment.service.impl.ThreadTaskService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 线下订单控制器
 * 
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/offline-order")
public class OfflineOrderController {

    @Autowired
    private IOfflineOrderService offlineOrderService;
    @Autowired
    private ThreadTaskService threadTaskService;

    /**
     * 查询线下订单列表
     * 
     * @param offlineOrder copy2offlineOrder 查询条件
     * @return 订单列表
     */

    @PostMapping("/list")
    public AjaxResult getOrderList(@RequestBody OfflineOrder offlineOrder) {
        try {
            offlineOrder.setPageNum((offlineOrder.getPageNum() - 1) * offlineOrder.getPageSize());
            List<OfflineOrder> list = offlineOrderService.selectOfflineOrderList(offlineOrder);
            int i = offlineOrderService.selectOfflineOrderCount(offlineOrder);
            return AjaxResult.success("查询成功")
                    .put("data", list)
                    .put("total", i);
        } catch (Exception e) {
            log.error("查询线下订单列表失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }
    @PostMapping("/tempList")
    public AjaxResult getTempOrderList(@RequestBody OfflineOrder offlineOrder) {
        try {
            offlineOrder.setPageNum((offlineOrder.getPageNum() - 1) * offlineOrder.getPageSize());
            List<OfflineOrder> list = offlineOrderService.selectOfflineOrderListTemp(offlineOrder);
            int i = offlineOrderService.selectOfflineOrderListTempCount(offlineOrder);
            return AjaxResult.success("查询成功")
                    .put("data", list)
                    .put("total", i);
        }catch (Exception e) {
            log.error("查询线下订单列表失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据ID查询线下订单详情
     * 
     * @param id 订单ID
     * @return 订单详情
     */
    @GetMapping("/detail/{id}")
    public AjaxResult getOrderDetail(@PathVariable Long id) {
        try {
            OfflineOrder order = offlineOrderService.selectOfflineOrderById(id);
            if (order != null) {
                return AjaxResult.success("查询成功", order);
            } else {
                return AjaxResult.error("订单不存在");
            }
        } catch (Exception e) {
            log.error("查询线下订单详情失败: id={}", id, e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据商户ID查询订单列表
     * 
     * @param merId 商户ID
     * @return 订单列表
     */
    @GetMapping("/merchant/{merId}")
    public AjaxResult getOrdersByMerchant(@PathVariable Long merId) {
        try {
            List<OfflineOrder> orders = offlineOrderService.selectOrdersByMerId(merId);
            return AjaxResult.success("查询成功")
                    .put("data", orders)
                    .put("total", orders.size());
        } catch (Exception e) {
            log.error("根据商户ID查询订单失败: merId={}", merId, e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据购买者openid查询订单列表
     * 
     * @param buyerOpenid 购买者openid
     * @return 订单列表
     */
    @GetMapping("/buyer/{buyerOpenid}")
    public AjaxResult getOrdersByBuyer(@PathVariable String buyerOpenid) {
        try {
            List<OfflineOrder> orders = offlineOrderService.selectOrdersByBuyerOpenid(buyerOpenid);
            return AjaxResult.success("查询成功")
                    .put("data", orders)
                    .put("total", orders.size());
        } catch (Exception e) {
            log.error("根据购买者openid查询订单失败: buyerOpenid={}", buyerOpenid, e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 创建线下订单
     * 
     * @param offlineOrder copy2offlineOrder  订单信息，需包含商户ID、购买者openid、商品名称、商品描述、支付金额
     * @return 创建结果
     */
    @PostMapping("/create")
    public AjaxResult createOrder(@RequestBody OfflineOrderDto offlineOrder) {

        try {

            PayRespData respData = offlineOrderService.createOrder(offlineOrder);
            log.info("创建线下订单成功:{}", respData);
            
            return AjaxResult.success("订单创建成功", respData);
            
        } catch (Exception e) {
            log.error("创建线下订单失败: merId={}, buyerOpenid={}", 
                     offlineOrder.getMerId(), offlineOrder.getBuyerOpenid(), e);
            return AjaxResult.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新线下订单
     * 
     * @param offlineOrder 订单信息
     * @return 更新结果
     */
    @PutMapping("/update")
    public AjaxResult updateOrder(@RequestBody OfflineOrder offlineOrder) {
        try {
            if (offlineOrder.getId() == null) {
                return AjaxResult.error("订单ID不能为空");
            }

            int result = offlineOrderService.updateOfflineOrder(offlineOrder);
            if (result > 0) {
                log.info("更新线下订单成功: orderId={}", offlineOrder.getId());
                return AjaxResult.success("更新成功");
            } else {
                return AjaxResult.error("更新失败，订单不存在");
            }
            
        } catch (Exception e) {
            log.error("更新线下订单失败: orderId={}", offlineOrder.getId(), e);
            return AjaxResult.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 更新订单支付状态
     * 
     * @param offlineOrder 订单信息，需包含订单ID和支付状态
     * @return 更新结果
     */
    @PutMapping("/pay-state")
    public AjaxResult updatePayState(@RequestBody OfflineOrder offlineOrder) {
        try {
            if (offlineOrder.getId() == null) {
                return AjaxResult.error("订单ID不能为空");
            }
            if (offlineOrder.getPayState() == null) {
                return AjaxResult.error("支付状态不能为空");
            }

            boolean success = offlineOrderService.updatePayState(offlineOrder);
            if (success) {
                log.info("更新订单支付状态成功: orderId={}, payState={}", 
                        offlineOrder.getId(), offlineOrder.getPayState());
                return AjaxResult.success("支付状态更新成功");
            } else {
                return AjaxResult.error("更新失败，订单不存在");
            }
            
        } catch (Exception e) {
            log.error("更新订单支付状态失败: orderId={}, payState={}", 
                     offlineOrder.getId(), offlineOrder.getPayState(), e);
            return AjaxResult.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 更新订单状态
     * 
     * @param offlineOrder 订单信息，需包含订单ID和订单状态
     * @return 更新结果
     */
    @PutMapping("/order-state")
    public AjaxResult updateOrderState(@RequestBody OfflineOrder offlineOrder) {
        try {
            if (offlineOrder.getId() == null) {
                return AjaxResult.error("订单ID不能为空");
            }
            if (offlineOrder.getOrderState() == null) {
                return AjaxResult.error("订单状态不能为空");
            }

            boolean success = offlineOrderService.updateOrderState(offlineOrder);
            if (success) {
                log.info("更新订单状态成功: orderId={}, orderState={}", 
                        offlineOrder.getId(), offlineOrder.getOrderState());
                return AjaxResult.success("订单状态更新成功");
            } else {
                return AjaxResult.error("更新失败，订单不存在");
            }
            
        } catch (Exception e) {
            log.error("更新订单状态失败: orderId={}, orderState={}", 
                     offlineOrder.getId(), offlineOrder.getOrderState(), e);
            return AjaxResult.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 删除线下订单
     * 
     * @param id 订单ID
     * @return 删除结果
     */
    @DeleteMapping("/{id}")
    public AjaxResult deleteOrder(@PathVariable Long id) {
        try {
            boolean success = offlineOrderService.removeById(id);
            if (success) {
                log.info("删除线下订单成功: orderId={}", id);
                return AjaxResult.success("删除成功");
            } else {
                return AjaxResult.error("删除失败，订单不存在");
            }
            
        } catch (Exception e) {
            log.error("删除线下订单失败: orderId={}", id, e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }
    //商户单独确认收货用的
    @PostMapping
    public AjaxResult SinglePay(@RequestParam("OrderCode") String OrderCode){
        return AjaxResult.success(threadTaskService.confirmSingleOrder(OrderCode));
    }
    @PostMapping("/refund")
    public AjaxResult Refund(@RequestBody OfflineOrder offlineOrder){
        return AjaxResult.success(offlineOrderService.RefundOfflineOrder(offlineOrder));
    }
}
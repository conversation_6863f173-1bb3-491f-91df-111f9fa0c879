package com.aggregate.payment.controller;

import com.aggregate.payment.common.AjaxResult;
import com.aggregate.payment.entity.MlCameraInfo;
import com.aggregate.payment.service.MlCameraInfoService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/camera")
public class MlCameraInfoController {
    @Autowired
    private MlCameraInfoService mlCameraInfoService;

    @PostMapping("/batchInsert")
    public AjaxResult batchInsert(@RequestBody List<MlCameraInfo> list) {
        int count = mlCameraInfoService.insertBatch(list);
        return AjaxResult.success(count);
    }

    @PostMapping("/batchUpdate")
    public AjaxResult batchUpdate(@RequestBody List<MlCameraInfo> list) {
        int count = mlCameraInfoService.updateBatch(list);
        return AjaxResult.success(count);
    }

    @PostMapping("/batchDelete")
    public AjaxResult batchDelete(@RequestBody List<Integer> ids) {
        int count = mlCameraInfoService.deleteBatchByIds(ids);
        return AjaxResult.success(count);
    }

    @PostMapping("/batchSelect")
    public AjaxResult batchSelect(@RequestBody List<Integer> ids) {
        List<MlCameraInfo> result = mlCameraInfoService.selectBatchByIds(ids);
        return AjaxResult.success(result);
    }

    @PostMapping("/page")
    public AjaxResult page(@RequestBody MlCameraInfo mlCameraInfo) {
        if (mlCameraInfo.getPageNum() == null) {
            mlCameraInfo.setPageNum(1);
        }
        if (mlCameraInfo.getPageSize() == null) {
            mlCameraInfo.setPageSize(10);
        }
        mlCameraInfo.setPageNum((mlCameraInfo.getPageNum() - 1) * mlCameraInfo.getPageSize());
        List<MlCameraInfo> list = mlCameraInfoService.selectByCondition(mlCameraInfo);
        int total = mlCameraInfoService.countByCondition(mlCameraInfo.getMerId(), mlCameraInfo.getVisible(), mlCameraInfo.getCameraName());
        return AjaxResult.success().put("list", list).put("total", total);
    }
    @PostMapping("/import")
    public AjaxResult importCameras(@RequestParam("merId") Integer merId) {
        List<MlCameraInfo> mlCameraInfos = mlCameraInfoService.importCameras(merId);
        return AjaxResult.success(mlCameraInfos);
    }
    @PostMapping("/playUrl")
    public AjaxResult playUrl(@RequestBody MlCameraInfo mlCameraInfo) {
        return AjaxResult.success(mlCameraInfoService.getPlayUrl(mlCameraInfo.getId()));

    }
} 
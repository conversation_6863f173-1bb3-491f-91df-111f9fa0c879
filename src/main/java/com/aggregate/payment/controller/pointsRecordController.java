package com.aggregate.payment.controller;

import com.aggregate.payment.common.AjaxResult;
import com.aggregate.payment.entity.Points2voucherDTO;
import com.aggregate.payment.entity.PointsRecord;
import com.aggregate.payment.entity.pointsSearch.PointsRecordQuery;
import com.aggregate.payment.entity.pointsSearch.PointsRecordVo;
import com.aggregate.payment.mapper.pointsRecordMapper;
import com.aggregate.payment.service.PointsRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/points")
public class pointsRecordController {
    @Autowired
    private PointsRecordService pointsRecordService;

    @Autowired
    private pointsRecordMapper pointsRecordMapper;
    @PostMapping("/onlineOrder")
    public AjaxResult pointsRecord(@RequestBody List<Integer> orderIds){
       return AjaxResult.success(pointsRecordService.InsertpointsRecord(orderIds));
    }
    @PostMapping("/userpoints2voucher")
    public AjaxResult updateUserpointsRecord(@RequestBody Points2voucherDTO dto){
        return AjaxResult.success(pointsRecordService.updateUserpointsRecord(dto));
    }
    @PostMapping("/merchantpoints2voucher")
    public AjaxResult updateMerchantpointsRecord(@RequestBody Points2voucherDTO dto){
        return AjaxResult.success(pointsRecordService.updateMerchantpointsRecord(dto));
    }

    /**
     * 查询用户积分汇总统计
     */
    @PostMapping("/summary")
    public AjaxResult getUserpointsSummary(@RequestBody PointsRecord pointsRecord) {
        try {
            Map<String, Object> summary = pointsRecordMapper.selectUserpointsSummary(pointsRecord);
            return AjaxResult.success("查询成功", summary);
        } catch (Exception e) {
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询用户积分明细列表（支持分页）
     */
    @PostMapping("/details")
    public AjaxResult getUserpointsDetails(@RequestBody PointsRecord pointsRecord) {
        try {
            // 处理分页参数：将pageNum转换为offset
            if (pointsRecord.getPageNum() != null && pointsRecord.getPageSize() != null) {
                int offset = (pointsRecord.getPageNum() - 1) * pointsRecord.getPageSize();
                pointsRecord.setPageNum(offset);
            }

            List<PointsRecord> details = pointsRecordMapper.selectUserpointsDetails(pointsRecord);
            int total = pointsRecordMapper.selectUserpointsDetailsCount(pointsRecord);
            return AjaxResult.success("查询成功")
                    .put("data", details)
                    .put("total", total);
        } catch (Exception e) {
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }
    @PostMapping("/pointsList")
    public AjaxResult getPointsList(@RequestBody PointsRecordQuery query) {
        try {
            List<PointsRecordVo> list = pointsRecordService.getPointsList(query);
            int total = pointsRecordService.getPointsListCount(query);
            return AjaxResult.success("查询成功")
                    .put("data", list)
                    .put("total", total);
        } catch (Exception e) {
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }
}

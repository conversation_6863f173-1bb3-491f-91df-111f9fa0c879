package com.aggregate.payment.controller;

import com.aggregate.payment.common.AjaxResult;
import com.aggregate.payment.dto.QrCodeGenerateRequest;
import com.aggregate.payment.dto.QrCodeStatusUpdateRequest;
import com.aggregate.payment.dto.QrCodeUpdateRequest;
import com.aggregate.payment.entity.AggregateQrCode;
import com.aggregate.payment.service.impl.AggregateQrCodeServiceImpl;
import com.aggregate.payment.util.QrCodeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpStatus;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

import java.util.List;

/**
 * 二维码管理控制器
 *
 * <AUTHOR>
 */
@Slf4j
@RestController
@RequestMapping("/qrcode")
public class QrCodeController {

    @Resource
    private AggregateQrCodeServiceImpl qrCodeService;
    @Value("${aggregate.payment.QRdomain}")
    private String QRdomain;
    /**
     * 创建新二维码（仅当商户没有二维码时）
     */
    @PostMapping("/create")
    public AjaxResult createQrCode(@RequestBody QrCodeGenerateRequest request) {
        try {
            String qrCodeKey = qrCodeService.createQrCode(
                    request.getMerchantId(),
                    request.getQrCodeType()
            );

            log.info("创建聚合二维码成功: merchantId={}, qrCodeKey={}", request.getMerchantId(), qrCodeKey);

            return AjaxResult.success("二维码创建成功")
                    .put("qrCodeKey", qrCodeKey)
                    .put("qrCodeUrl", QRdomain + "/aggregate-pay/scan?code=" + qrCodeKey)
                    .put("qrCodeImageUrl", QRdomain + "/aggregate-pay/qrcode/image/" + qrCodeKey);

        } catch (Exception e) {
            log.error("创建聚合二维码失败", e);
            return AjaxResult.error("创建失败: " + e.getMessage());
        }
    }

    /**
     * 更新现有二维码
     */
    @PutMapping("/update")
    public AjaxResult updateQrCode(@RequestBody QrCodeUpdateRequest request) {
        try {
            boolean success = qrCodeService.updateQrCode(
                    request.getMerchantId(),

                    request.getQrCodeType()
            );

            if (success) {
                // 获取更新后的二维码信息
                AggregateQrCode qrCode = qrCodeService.selectByMerchantId(request.getMerchantId());

                log.info("更新聚合二维码成功: merchantId={}, qrCodeKey={}", request.getMerchantId(), qrCode.getQrCodeKey());

                return AjaxResult.success("二维码更新成功")
                        .put("qrCodeKey", qrCode.getQrCodeKey())
                        .put("qrCodeUrl", qrCode.getQrCodeUrl())
                        .put("qrCodeImageUrl", QRdomain + "/aggregate-pay/qrcode/image/" + qrCode.getQrCodeKey());
            } else {
                return AjaxResult.error("更新失败");
            }

        } catch (Exception e) {
            log.error("更新聚合二维码失败", e);
            return AjaxResult.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 生成或更新聚合二维码（智能判断）
     */
    @PostMapping("/generate")
    public AjaxResult generateOrUpdateQrCode(@RequestBody QrCodeGenerateRequest request) {
        try {
            String qrCodeKey = qrCodeService.generateOrUpdateQrCode(
                    request.getMerchantId(),
                    request.getQrCodeType()
            );

            log.info("生成/更新聚合二维码成功: merchantId={}, qrCodeKey={}", request.getMerchantId(), qrCodeKey);

            return AjaxResult.success("二维码操作成功")
                    .put("qrCodeKey", qrCodeKey)
                    .put("qrCodeUrl", QRdomain + "/aggregate-pay/scan?code=" + qrCodeKey)
                    .put("qrCodeImageUrl", QRdomain + "/aggregate-pay/qrcode/image/" + qrCodeKey);

        } catch (Exception e) {
            log.error("生成/更新聚合二维码失败", e);
            return AjaxResult.error("操作失败: " + e.getMessage());
        }
    }

    /**
     * 查询二维码列表
     */
    @GetMapping("/list")
    public AjaxResult getQrCodeList(@RequestParam(required = false) Long merchantId,
                                   @RequestParam(required = false) String qrCodeType,
                                   @RequestParam(required = false) String status) {
        try {
            AggregateQrCode queryParam = new AggregateQrCode();
            queryParam.setMerchantId(merchantId);
            queryParam.setQrCodeType(qrCodeType);
            queryParam.setStatus(status);

            List<AggregateQrCode> list = qrCodeService.selectList(queryParam);

            return AjaxResult.success("查询成功")
                    .put("data", list)
                    .put("total", list.size());

        } catch (Exception e) {
            log.error("查询二维码列表失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 查询二维码详情
     */
    @GetMapping("/detail/{qrCodeKey}")
    public AjaxResult getQrCodeDetail(@PathVariable String qrCodeKey) {
        try {
            AggregateQrCode qrCode = qrCodeService.selectByQrCodeKey(qrCodeKey);

            if (qrCode != null) {
                return AjaxResult.success("查询成功", qrCode);
            } else {
                return AjaxResult.error("二维码不存在");
            }

        } catch (Exception e) {
            log.error("查询二维码详情失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 根据商户ID查询二维码
     */
    @GetMapping("/merchant/{merchantId}")
    public AjaxResult getQrCodeByMerchant(@PathVariable Long merchantId) {
        try {
            AggregateQrCode qrCode = qrCodeService.selectByMerchantId(merchantId);

            if (qrCode != null) {
                return AjaxResult.success("查询成功", qrCode)
                        .put("qrCodeImageUrl", QRdomain + "/aggregate-pay/qrcode/image/" + qrCode.getQrCodeKey());
            } else {
                return AjaxResult.error("该商户暂无二维码");
            }

        } catch (Exception e) {
            log.error("查询商户二维码失败", e);
            return AjaxResult.error("查询失败: " + e.getMessage());
        }
    }

    /**
     * 更新二维码状态
     */
    @PutMapping("/status")
    public AjaxResult updateQrCodeStatus(@RequestBody QrCodeStatusUpdateRequest request) {
        try {
            AggregateQrCode qrCode = qrCodeService.selectByQrCodeKey(request.getQrCodeKey());
            if (qrCode != null) {
                qrCode.setStatus(request.getStatus());
                qrCodeService.updateById(qrCode);
                return AjaxResult.success("状态更新成功");
            } else {
                return AjaxResult.error("二维码不存在");
            }

        } catch (Exception e) {
            log.error("更新二维码状态失败", e);
            return AjaxResult.error("更新失败: " + e.getMessage());
        }
    }

    /**
     * 获取二维码图片
     */
    @GetMapping("/image/{qrCodeKey}")
    public ResponseEntity<byte[]> getQrCodeImage(@PathVariable String qrCodeKey) {
        try {
            // 根据二维码标识查询二维码信息
            AggregateQrCode qrCode = qrCodeService.selectByQrCodeKey(qrCodeKey);

            if (qrCode == null) {
                log.warn("二维码不存在: {}", qrCodeKey);
                return ResponseEntity.notFound().build();
            }

            if (!"0".equals(qrCode.getStatus())) {
                log.warn("二维码已停用: {}", qrCodeKey);
                return ResponseEntity.status(HttpStatus.FORBIDDEN).build();
            }

            // 生成二维码图片
            byte[] imageBytes = QrCodeUtil.generateQrCodeImage(qrCode.getQrCodeUrl());

            // 设置响应头
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.IMAGE_PNG);
            headers.setContentLength(imageBytes.length);
            headers.setCacheControl("no-cache");

            log.info("生成二维码图片成功: {}", qrCodeKey);

            return new ResponseEntity<>(imageBytes, headers, HttpStatus.OK);

        } catch (Exception e) {
            log.error("获取二维码图片失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }

    /**
     * 获取二维码Base64图片
     */
    @GetMapping("/base64/{qrCodeKey}")
    public AjaxResult getQrCodeBase64(@PathVariable String qrCodeKey) {
        try {
            // 根据二维码标识查询二维码信息
            AggregateQrCode qrCode = qrCodeService.selectByQrCodeKey(qrCodeKey);

            if (qrCode == null) {
                return AjaxResult.error("二维码不存在");
            }

            if (!"0".equals(qrCode.getStatus())) {
                return AjaxResult.error("二维码已停用");
            }

            // 生成二维码Base64图片
            String base64Image = QrCodeUtil.generateQrCodeBase64(qrCode.getQrCodeUrl());

            log.info("生成二维码Base64图片成功: {}", qrCodeKey);

            return AjaxResult.success("获取成功")
                    .put("qrCodeKey", qrCodeKey)
                    .put("qrCodeUrl", qrCode.getQrCodeUrl())
                    .put("imageBase64", "data:image/png;base64," + base64Image);

        } catch (Exception e) {
            log.error("获取二维码Base64图片失败: {}", e.getMessage(), e);
            return AjaxResult.error("获取失败: " + e.getMessage());
        }
    }
}

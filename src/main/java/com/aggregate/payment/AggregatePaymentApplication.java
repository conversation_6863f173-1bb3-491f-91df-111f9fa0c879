package com.aggregate.payment;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

/**
 * 聚合码支付系统启动类
 * 
 * <AUTHOR>
 */
@SpringBootApplication
@EnableScheduling
@EnableAsync
@MapperScan("com.aggregate.payment.mapper")
public class AggregatePaymentApplication {

    public static void main(String[] args) {
        SpringApplication.run(AggregatePaymentApplication.class, args);
        System.out.println("聚合码支付系统启动成功！");
    }
}

package com.aggregate.payment.enums;

/**
 * 用户初始化结果枚举
 * 
 * <AUTHOR>
 */
public enum UserInitResult {
    
    /** 用户已存在且已绑定手机号，可以正常使用 */
    SUCCESS(0, "用户验证成功"),
    
    /** 需要绑定手机号（新用户或未绑定手机号的老用户） */
    NEED_BIND_PHONE(1, "需要绑定手机号"),
    
    /** 初始化失败 */
    FAILED(-1, "用户初始化失败");
    
    private final int code;
    private final String message;
    
    UserInitResult(int code, String message) {
        this.code = code;
        this.message = message;
    }
    
    public int getCode() {
        return code;
    }
    
    public String getMessage() {
        return message;
    }
}

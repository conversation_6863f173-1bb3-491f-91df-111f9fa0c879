# 聚合码支付系统

## 项目简介

这是一个独立的聚合码支付系统，支持微信和支付宝扫码支付，可以为商户生成聚合二维码，用户扫码后自动识别支付方式并获取用户openid。

## 核心功能

1. **聚合码生成**：为商户生成统一的聚合二维码
2. **自动识别**：根据扫码工具自动识别微信或支付宝
3. **用户授权**：获取用户openid用于后续支付流程
4. **服务费配置**：支持配置平台服务费比例
5. **扫码统计**：记录扫码次数和时间

## 技术栈

- Spring Boot 2.5.15
- MyBatis Plus 3.5.3
- MySQL 8.0
- Redis
- FastJSON 2.0
- Hutool 5.8
- Lombok

## 项目结构

```
aggregate-payment/
├── src/main/java/com/aggregate/payment/
│   ├── AggregatePaymentApplication.java    # 启动类
│   ├── entity/                             # 实体类
│   │   ├── Merchant.java                   # 商户实体
│   │   ├── AggregateQrCode.java           # 聚合码实体
│   │   └── PaymentRecord.java             # 支付记录实体
│   ├── mapper/                            # Mapper接口
│   ├── service/                           # 服务层
│   │   ├── AggregateQrCodeService.java    # 聚合码服务
│   │   └── PaymentAuthService.java        # 支付授权服务
│   ├── controller/                        # 控制器
│   │   ├── ScanController.java            # 扫码控制器
│   │   ├── AuthController.java            # 授权回调控制器
│   │   └── QrCodeController.java          # 二维码管理控制器
│   └── config/                            # 配置类
└── src/main/resources/
    ├── application.yml                     # 配置文件
    └── sql/aggregate_payment.sql          # 数据库初始化脚本
```

## 快速开始

### 1. 环境准备

- JDK 1.8+
- MySQL 8.0+
- Redis
- Maven 3.6+

### 2. 数据库初始化

执行 `src/main/resources/sql/aggregate_payment.sql` 脚本创建数据库和表。

### 3. 配置修改

修改 `application.yml` 中的数据库和Redis连接信息：

```yaml
spring:
  datasource:
    url: *********************************************
    username: your_username
    password: your_password
  redis:
    host: localhost
    port: 6379
```

### 4. 启动项目

```bash
mvn spring-boot:run
```

项目启动后访问：http://localhost:8081/aggregate-pay

## 核心流程

### 1. 聚合码生成流程

```
商户申请 → 生成唯一标识 → 构建扫码URL → 保存到数据库 → 返回二维码
```

### 2. 扫码授权流程

```
用户扫码 → 检测User-Agent → 重定向到对应平台授权 → 用户确认授权 → 获取openid
```

### 3. User-Agent识别

- **微信扫码**：User-Agent包含 `MicroMessenger`
- **支付宝扫码**：User-Agent包含 `AlipayClient`
- **其他扫码**：显示支付方式选择页面

## API接口

### 1. 生成聚合码

```http
POST /qrcode/generate
Content-Type: application/json

{
    "merchantId": 1,
    "merchantCode": "MERCHANT_001",
    "serviceFeeRate": 0.006,
    "qrCodeType": "2",
    "fixedAmount": 100.00
}
```

### 2. 查询二维码列表

```http
GET /qrcode/list?merchantId=1&status=0
```

### 3. 扫码入口

```http
GET /scan?code=MERCHANT_001_1234567890_ABC123
```

## 测试方法

### 1. 生成测试二维码

```bash
curl -X POST http://localhost:8081/aggregate-pay/qrcode/generate \
  -H "Content-Type: application/json" \
  -d '{
    "merchantId": 1,
    "merchantCode": "MERCHANT_001",
    "serviceFeeRate": 0.006
  }'
```

### 2. 模拟扫码测试

```bash
# 模拟微信扫码
curl -H "User-Agent: Mozilla/5.0 MicroMessenger/8.0.20" \
  http://localhost:8081/aggregate-pay/scan?code=YOUR_QR_CODE_KEY

# 模拟支付宝扫码
curl -H "User-Agent: Mozilla/5.0 AlipayClient/10.2.36" \
  http://localhost:8081/aggregate-pay/scan?code=YOUR_QR_CODE_KEY
```

## 配置说明

### 微信配置

在 `application.yml` 中配置微信应用信息：

```yaml
aggregate:
  payment:
    wechat:
      app-id: wx1234567890abcdef
      app-secret: 1234567890abcdef1234567890abcdef
```

### 支付宝配置

```yaml
aggregate:
  payment:
    alipay:
      app-id: 2021000000000000
      private-key: MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQC...
      public-key: MIIBIjANBgkqhkiG9w0BAQEFAAOCAQ8AMIIBCgKCAQEA...
      gateway-url: https://openapi.alipay.com/gateway.do
```

## 注意事项

1. **域名配置**：生产环境需要配置正确的域名和HTTPS
2. **授权回调**：需要在微信和支付宝后台配置正确的回调地址
3. **安全性**：生产环境需要添加签名验证和防重放攻击
4. **日志监控**：建议添加详细的日志记录和监控

## 后续扩展

1. 支持更多支付方式（银联、数字人民币等）
2. 添加支付流程和订单管理
3. 增加商户管理后台
4. 支持二维码图片生成和下载
5. 添加数据统计和报表功能
